# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start dev server with hot reload
- `pnpm build` - Build for production
- `pnpm lint` - Run ESLint
- `pnpm preview` - Preview production build

### Package Management
- Uses `pnpm` as package manager (configured in package.json)
- Node.js with TypeScript and Vite

## Architecture Overview

This is a **React-based AI content generation platform** built with TypeScript, Vite, and Zustand state management, targeting Chinese users with social media optimization.

### Core Purpose
- **AI-powered content generation**: Images, videos, and articles
- **Advanced image editing**: Layer-based manipulation with AI assistance
- **Creative workflow management**: Job tracking, batch processing, exports
- **Social media optimization**: Pre-configured aspect ratios for Chinese platforms

### Key Directories

#### `/src/routes/` - Application Pages
- `/` - Home (main creation interface)
- `/creation/:jobid` - Job detail and editing
- `/advanced-edit` - Advanced editing with history
- `/advanced-edit/new` - Sophisticated image editing
- `/login` - Authentication

#### `/src/stores/` - State Management (Zustand)
- `useCommonStore` - Auth, global state
- `useCreationStore` - Main AI generation engine
- `useAdvancedEditStore` - Advanced editing features
- `useEditStore` - Basic layer management
- `useCutBoxStore` - Canvas positioning
- `useEstablishingShotStore` - Reference images

#### `/src/components/` - UI Components
- **Creation components** - Job generation interface
- **Advanced editing** - Canvas-based editing tools
- **UI/** - shadcn/ui design system components

#### `/src/config/index.ts` - Configuration
- **API endpoints**: Production API at `https://ai-demo-api.elephantailab.com/api`
- **Midjourney API**: `https://api.fotomore.com/api/creations/v1`
- **Social media presets**: WeChat, Weibo, Xiaohongshu, Douyin aspect ratios
- **Image proportions**: Standard and social media optimized ratios

### Technology Stack
- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **State**: Zustand stores
- **Routing**: React Router v7
- **Build**: Vite with SWC
- **Package Manager**: pnpm
- **UI Library**: Radix UI components via shadcn/ui
- **Icons**: Lucide React
- **Date**: dayjs with Chinese locale

### Key Features
- **Multi-modal AI generation**: Image, video, and article generation
- **Job types**: 11 distinct job types (variation, upscale, remix, pan, background removal, video generation/extend/upscale, article generation)
- **Layer-based editing**: Advanced image manipulation with canvas
- **Reference system**: Style, content, and face references
- **Social media optimization**: Pre-configured for Chinese platforms (WeChat, Weibo, Xiaohongshu, Douyin)
- **Real-time status tracking**: WebSocket-like job status updates
- **Export capabilities**: High-quality downloads for professional use

### Development Notes
- **Language**: Chinese UI with English code comments
- **Environment**: Production API configured by default
- **Testing**: No test framework configured (only ESLint)
- **Fonts**: Custom Chinese fonts in `/public/fonts/`
- **Workers**: Uses Comlink for web worker communication in `/src/workers/`