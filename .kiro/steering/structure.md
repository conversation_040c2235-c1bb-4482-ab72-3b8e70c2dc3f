# Project Structure

## Directory Organization

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components (shadcn/ui)
│   ├── creation-*      # Creation workflow components
│   ├── advanced-*      # Advanced editing components
│   └── *.tsx           # Feature-specific components
├── routes/             # Page components and routing
├── stores/             # Zustand state management
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and configurations
├── config/             # Application configuration
├── workers/            # Web workers for background processing
├── loader/             # React Router data loaders
└── assets/             # Static assets
```

## Key Conventions

### Component Organization
- **UI Components**: Located in `src/components/ui/` - reusable, accessible components
- **Feature Components**: Named with feature prefix (e.g., `creation-*`, `advanced-*`)
- **Page Components**: Located in `src/routes/` - top-level route components

### State Management
- **Zustand Stores**: Located in `src/stores/` with descriptive names
- **Persistence**: Uses localStorage with JSON storage for state persistence
- **Store Structure**: Separate state and actions, use TypeScript interfaces

### Styling Conventions
- **TailwindCSS**: Primary styling approach with custom design tokens
- **CSS Variables**: HSL color system with light/dark theme support
- **Custom Fonts**: Multiple Chinese fonts loaded via @font-face
- **Component Variants**: Use `class-variance-authority` for component styling

### File Naming
- **Components**: PascalCase for component files (e.g., `CreationEdit.tsx`)
- **Utilities**: kebab-case for utility files (e.g., `use-mobile.tsx`)
- **Stores**: kebab-case with descriptive names (e.g., `creation.ts`)

### Import Conventions
- Use `@/` path alias for src directory imports
- Group imports: external libraries, internal components, utilities
- Prefer named exports over default exports for utilities

### TypeScript Patterns
- Define interfaces for complex data structures
- Use enums for status and type constants
- Strict typing for API responses and store state