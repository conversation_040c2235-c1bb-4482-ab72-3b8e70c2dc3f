# Product Overview

This is an AI-powered image and video generation application built with React. The application provides a comprehensive creative workflow for generating and editing visual content using AI models.

## Core Features

- **Multi-modal Content Generation**: Support for images, videos, and articles
- **Advanced Image Editing**: Layer management, composition tools, and real-time preview
- **Image Operations**: Variation, upscaling, remixing, panning, outpainting, and background removal
- **Video Processing**: Video generation from images, video extension, and upscaling
- **Search Integration**: Built-in image search with VCG image library integration
- **User Management**: Authentication system with job history tracking

## Key User Flows

- **Home Page**: Browse and discover generated content
- **Creation Workflow**: Generate content with prompts and reference images
- **Creation Detail**: View and manage individual generation jobs with editing options
- **Advanced Editing**: Professional editing interface with layer manipulation
- **Upload Management**: Handle file uploads and processing

## Generation Types

- **Image Generation**: Text-to-image with style/content/face references
- **Video Generation**: Image-to-video and video extension capabilities  
- **Article Generation**: Text content creation (planned feature)

The application focuses on providing an intuitive creative workflow while maintaining professional-grade editing capabilities for content creators and designers.