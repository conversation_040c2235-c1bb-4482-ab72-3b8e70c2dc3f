# Technology Stack

## Core Technologies
- **React 18** with TypeScript for the frontend
- **Vite** as the build tool and dev server
- **React Router 7** for client-side routing
- **Zustand** for state management with persistence
- **TailwindCSS** for styling with custom design system

## UI Framework
- **Radix UI** components for accessible primitives
- **Shadcn/ui** component library built on Radix
- **Lucide React** for icons
- **Sonner** for toast notifications

## Key Libraries
- **ahooks** for React hooks utilities
- **ky** for HTTP requests with interceptors
- **zod** for schema validation
- **react-hook-form** with resolvers for form handling
- **next-themes** for theme management
- **comlink** for web worker communication
- **dom-to-image** for canvas/image generation
- **idb-keyval** for IndexedDB storage

## Development Tools
- **ESLint** with TypeScript rules
- **PostCSS** with Autoprefixer
- **PNPM** as package manager
- **SWC** for fast compilation

## Common Commands
```bash
# Development
pnpm dev              # Start dev server
pnpm build            # Build for production
pnpm preview          # Preview production build
pnpm lint             # Run ESLint

# Package management
pnpm install          # Install dependencies
pnpm add <package>    # Add new dependency
```

## Build Configuration
- Uses Vite with React SWC plugin for fast builds
- Path alias `@/` maps to `src/` directory
- Web workers supported via comlink plugin
- TypeScript strict mode enabled