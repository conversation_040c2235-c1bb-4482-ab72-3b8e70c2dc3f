import { webapi } from "@/lib/utils"
import { DiffusionDataType, DiffusionStatus, JobType, ResponseType, useCreationStore } from "@/stores/creation";
import { LoaderFunctionArgs } from "react-router"

export default async function clientLoader({ params }: LoaderFunctionArgs) {
  const { jobid: jobId = '' } = params;
  let localeJob = useCreationStore.getState().getLocalJob(jobId);

  if (!localeJob) {
    const { data, message } = await webapi.get("creations/v1/job", {
      searchParams: {
        jobId,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (data) {
      const {
        incr_id,
        id,
        text,
        status,
        type,
        seed,
        comment,
        urls,
        created_at,
        updated_at,
        is_copyright,
        basemap
      } = data;
      localeJob = {
        id: incr_id,
        job_id: id,
        text,
        status,
        type,
        seed,
        comment,
        updated_at,
        urls,
        created_at,
        is_copyright,
        basemap,
      }
    } else {
      localeJob = {
        id: 0,
        job_id: jobId,
        text: '',
        status: DiffusionStatus.FAILED,
        type: JobType.DEFAULT,
        created_at: '',
        updated_at: '',
        comment: message || '获取任务信息接口调用失败，请稍后重试',
      }
    }
  }

  return localeJob;
}