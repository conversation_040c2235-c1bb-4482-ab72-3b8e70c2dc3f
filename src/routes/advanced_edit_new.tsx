import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { imageProportions } from "@/config";
import { calculateRatio, cn } from "@/lib/utils";
import AdvancedEditInput from "@/components/advanced-edit-input";
import AdvancedEditView from "@/components/advanced-edit";
import { But<PERSON> } from "@/components/ui/button";
import { CircleMinus, CirclePlus, Download, Eraser, Move, Plus, Undo2, Upload } from "lucide-react";
import { NavLink } from "react-router";
import { ActiveType, useAdvancedEditStore } from "@/stores/advanced-edit";
import AdvancedJobBox from "@/components/advanced-job-box";
import { Slider } from "@/components/ui/slider";
import AdvancedLayerList from "@/components/advanced-layer-list";
import AdvancedUploadDialog from "@/components/advanced-upload-dialog";

export default function AdvancedEditNew() {
  const {
    activeType,
    setActiveType,
    worktop,
    setWorktop,
    clearForegrounds,
    jobList,
    canDownload,
    downloadJobImage,
    currentForeground,
    autoRemoveBackground,
    setPrompt,
    setEstablishingShot,
    operationType,
    setOperationType,
    brushSize,
    setBrushSize,
    // foregrounds
  } = useAdvancedEditStore();

  return (
    <div className="flex flex-row h-screen overflow-hidden">
      <div className="overflow-hidden relative h-full w-60 lg:w-72 bg-muted/50 flex flex-col gap-4">
        <ScrollArea className="overflow-hidden relative w-full flex-1">
          <div className="p-4">
            <Tabs value={activeType} onValueChange={(value) => setActiveType(value as ActiveType)} className="w-full">
              <TabsList className="sticky top-4 left-4 z-10 w-full">
                <TabsTrigger value="edit" className="flex-1">编辑</TabsTrigger>
                <TabsTrigger value="retexture" className="flex-1">转绘</TabsTrigger>
              </TabsList>
              <TabsContent value="edit" className="relative flex flex-col gap-4">
                <div className="grid gap-2 mt-2">
                  <AdvancedUploadDialog>
                    <Button variant="outline" className="w-full">
                      <Upload />
                      上传图片
                    </Button>
                  </AdvancedUploadDialog>
                  {/* <Button variant="outline" className="w-full">
                  <Trash />
                  清空图层
                </Button> */}
                </div>
                {currentForeground && (<>
                  <div className="grid gap-2">
                    <Button
                      variant={operationType === 'move' ? "default" : "outline"}
                      className="w-full"
                      onClick={() => setOperationType('move')}
                    >
                      <Move />
                      移动缩放
                    </Button>
                    <Button
                      variant={operationType === 'erase' ? "default" : "outline"}
                      className="w-full"
                      onClick={() => setOperationType('erase')}
                    >
                      <CircleMinus />
                      涂抹修改
                    </Button>
                    <Button
                      variant={operationType === 'reverseErase' ? "default" : "outline"}
                      className="w-full"
                      onClick={() => setOperationType('reverseErase')}
                    >
                      <CirclePlus />
                      反向涂抹
                    </Button>
                  </div>
                  <div className="grid gap-2">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={autoRemoveBackground}
                    >
                      <Eraser />
                      去除背景
                    </Button>
                  </div>
                </>)}
                {/* <div className="grid gap-2">
                <Button variant="outline" className="w-full">
                  <PartyPopper />
                  建议提示词
                </Button>
              </div> */}
                {worktop && (<>
                  <div className="grid gap-3">
                    <div className="flex items-center gap-2">
                      <Label>常见图片比例</Label>
                      <p className="ml-auto text-sm">{calculateRatio(worktop?.width || 0, worktop?.height || 0)}</p>
                    </div>
                    <div className="grid grid-cols-4 border rounded-lg overflow-hidden">
                      {imageProportions.map((item) => (
                        <div
                          className={cn("rounded relative cursor-pointer bg-muted text-center p-2 border -m-[1px]", {
                            "bg-primary": item.width / item.height === worktop?.width / worktop?.height,
                            "text-primary-foreground": item.width / item.height === worktop?.width / worktop?.height,
                          })}
                          key={item.id}
                          onClick={() => setWorktop({
                            width: item.width,
                            height: item.height,
                          })}
                        >
                          <span className="select-none text-xs">{item.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  {operationType !== 'move' && (<div className="grid gap-3">
                    <div className="flex items-center gap-2">
                      <Label>笔刷尺寸</Label>
                      <p className="ml-auto text-sm">{brushSize}</p>
                    </div>
                    <div className="">
                      <Slider defaultValue={[50]} value={[brushSize]} max={100} min={10} step={1} onValueChange={(value) => setBrushSize(value[0])} />
                    </div>
                  </div>)}
                  {canDownload && <div className="grid gap-2 mt-auto">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={downloadJobImage}
                    >
                      <Download />
                      下载图片
                    </Button>
                  </div>}
                </>)}
              </TabsContent>
              <TabsContent value="retexture" className="relative flex flex-col gap-4">
                <div className="grid gap-2 text-sm">
                  <p>转绘功能支持在更改图片风格/内容的同时，尽量保持图片结构。</p>
                  <p>为保证生成效果，请使用和原图结构不冲突的提示词。</p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
        <AdvancedLayerList />
      </div>
      <div className="flex-1 relative p-4 h-full flex flex-col gap-4 overflow-hidden">
        <div className="flex-1 h-full">
          <AdvancedEditView />
        </div>
        <AdvancedEditInput className="z-10" />
      </div>
      <ScrollArea className="overflow-hidden relative h-full w-60 lg:w-72 bg-muted/50">
        <div className="flex flex-col gap-3 p-4">
          <div className="flex items-center justify-between gap-2 sticky top-4 left-4 z-10 w-full">
            <NavLink to="/advanced-edit" viewTransition>
              <Button variant="outline" size="sm">
                <Undo2 /> 返回记录
              </Button>
            </NavLink>
            <Button variant="outline" size="sm" onClick={() => {
              setWorktop(null);
              clearForegrounds();
              setActiveType('edit');
              setPrompt('');
              setOperationType('move');
              setEstablishingShot([]);
            }}>
              <Plus /> 新建编辑
            </Button>
          </div>
          <div className="relative">
            {jobList.map((item) => (
              <AdvancedJobBox key={item.id} job={item} />
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
