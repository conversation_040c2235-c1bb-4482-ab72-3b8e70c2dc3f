import { Outlet } from 'react-router'
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { ModeToggle } from '@/components/mode-toggle'
import { Toaster } from '@/components/ui/sonner'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useCommonStore } from '@/stores/common'

export default function Root() {
  const { user, logout } = useCommonStore();

  return (
    <>
      <div className="grid h-screen w-full overflow-hidden overscroll-none">
        <aside
          className="fixed right-0 pr-4 bottom-4 z-[9999] translate-x-3/4 transition-transform duration-300 hover:translate-x-0"
        >
          <nav className="grid gap-1 p-1 rounded-xl border shadow-lg backdrop-blur-lg">
            <Tooltip>
              <TooltipTrigger asChild>
                <ModeToggle />
              </TooltipTrigger>
              <TooltipContent side="right" sideOffset={5}>
                Toggle Theme
              </TooltipContent>
            </Tooltip>
            {user && <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Avatar className="size-9 rounded-lg cursor-pointer">
                  <AvatarImage src={undefined} alt={user.name} />
                  <AvatarFallback className="rounded-lg">{user.name.slice(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                side="right"
                align="end"
                sideOffset={12}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="size-8 rounded-lg">
                      <AvatarImage src={undefined} alt={user.name} />
                      <AvatarFallback className="rounded-lg">{user.name.slice(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">{user.name}</span>
                      <span className="text-muted-foreground truncate text-xs">
                        {user.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <User className="size-4" />
                    <span>Account</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <span>Billing</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <span>Notifications</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator /> */}
                <DropdownMenuItem onClick={logout}>
                  {/* <IconLogout /> */}
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>}
          </nav>
        </aside>
        <Outlet />
      </div>
      <div className="h-0">
        <Toaster position="top-center" />
      </div>
    </>
  )
}