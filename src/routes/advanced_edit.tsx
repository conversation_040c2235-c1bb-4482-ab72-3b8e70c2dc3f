import { ArrowRight } from "lucide-react";
import { NavLink } from "react-router";


export default function AdvancedEdit() {
  return (
    <div className="relative">
      <h1 className="text-xl px-4 py-2">高级编辑</h1>
      <div className="px-4 py-2">
        <NavLink to="/advanced-edit/new" className="flex w-80 h-36 justify-between items-end gap-2 p-4 rounded-lg border" viewTransition>
          <p className="text-base">开始编辑</p>
          <ArrowRight />
        </NavLink>
      </div>
      <div className="p-4">
        <p>历史记录</p>
      </div>
    </div>
  )
}
