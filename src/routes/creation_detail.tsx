import CreationEdit from "@/components/creation-edit";
import creationDetailLoader from "@/loader/creation_detail";
import { useLoaderData, useSearchParams } from "react-router";

export default function CreationDetail() {
  const data = useLoaderData<typeof creationDetailLoader>();
  const [searchParams] = useSearchParams();
  const index = searchParams.get('index') || 0;

  return (
    <div className="flex h-full overflow-y-auto flex-col overscroll-none">
      <CreationEdit job={data} index={Number(index)} />
    </div>
  )
}