import { create } from 'zustand'

interface Layer {
  id: number,
  x: number,
  y: number,
  rotate: number,
}

export interface TextLayer extends Layer {
  type: 'text',
  text: string,
  width: number | 'auto',
  height: number | 'auto',
  direction?: 'ltr' | 'rtl',
  writingMode?: 'horizontal-tb' | 'vertical-rl' | 'vertical-lr' | 'sideways-rl' | 'sideways-lr',
  fontSize: number,
  fontFamily?: string,
  fontWeight?: 'bold',
  fontStyle?: 'italic',
  textDecorationLine?: 'underline' | 'line-through' | 'overline' | 'none',
  textDecorationStyle?: 'solid' | 'double' | 'dotted' | 'dashed' | 'wavy',
  fontColor?: string,
}

export interface ImageLayer extends Layer {
  type: 'image',
  width: number,
  height: number,
  aspectRatio: number,
  base64: string,
  url?: string,
  rmbgBase64?: string,
}

export type LayerType = TextLayer | ImageLayer

type State = {
  basic: ImageLayer | null,
  layers: LayerType[],
  currentLayer: LayerType | null,
}
type Actions = {
  setBasic: (basic: ImageLayer) => void
  setCurrentLayer: (layer: LayerType | null) => void
  addLayer: (layer: LayerType) => void
  removeLayer: (layer: LayerType) => void
  updateLayer: (layer: LayerType) => void
  moveLayerUp: () => void
  moveLayerDown: () => void
  clearLayers: () => void
}

export const useEditStore = create<State & Actions>((set, get) => ({
  basic: null,
  layers: [],
  currentLayer: null,
  setBasic: (basic) => set({ basic }),
  setCurrentLayer: (layer) => set({ currentLayer: layer }),
  addLayer: (layer) => set({ layers: [...get().layers, layer] }),
  removeLayer: (layer) => set({ layers: get().layers.filter(l => l.id !== layer.id) }),
  updateLayer: (layer) => set({ layers: get().layers.map(l => l.id === layer.id ? layer : l) }),
  moveLayerDown: () => {
    const layer = get().currentLayer;
    if (!layer) return;
    const index = get().layers.findIndex(l => l.id === layer.id);
    if (index > 0) {
      const newLayers = [...get().layers];
      [newLayers[index], newLayers[index - 1]] = [newLayers[index - 1], newLayers[index]];
      set({ layers: newLayers });
    }
  },
  moveLayerUp: () => {
    const layer = get().currentLayer;
    if (!layer) return;
    const index = get().layers.findIndex(l => l.id === layer.id);
    if (index < get().layers.length - 1) {
      const newLayers = [...get().layers];
      [newLayers[index], newLayers[index + 1]] = [newLayers[index + 1], newLayers[index]];
      set({ layers: newLayers });
    }
  },
  clearLayers: () => set({ layers: [] }),
}))
