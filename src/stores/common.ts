import { setLocalToken, webapi } from '@/lib/utils'
import { toast } from 'sonner'
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

type UserType = {
  id: string,
  name: string,
  email: string,
  avatar: string,
}

type State = {
  loading: boolean,
  user: UserType | null,
}

type Actions = {
  setLoading: (loading: boolean) => void
  setUser: (user: UserType | null) => void
  logout: () => void
  login: (name: string, password: string) => void
}

export const useCommonStore = create<State & Actions>()(
  persist((set) => ({
    loading: false,
    user: null,
    setLoading: (loading) => set({ loading }),
    setUser: (user) => set({ user }),
    logout: () => {
      set({ user: null })
      localStorage.removeItem('t')
      window.location.href = '/login'
    },
    login: async (name, password) => {
      if (!name || !password) {
        toast.error('请输入用户名和密码')
        return
      }
      set({ loading: true, user: null })
      try {
        const res = await webapi.post('login', {
          json: {
            name,
            password
          }
        }).json<{
          status_code: number,
          message: string,
          data: {
            access_token: string,
            user: UserType
          }
        }>()

        if (res.status_code === 1) {
          const { access_token, user = {
            id: 'test',
            name: 'test',
            email: '<EMAIL>',
            avatar: 'https://picsum.photos/200/200'
          } } = res.data;
          const url = new URL(window.location.href)
          const redirect = url.searchParams.get('redirect')
          setLocalToken(access_token)
          set({ user })
          toast.success('登录成功，正在跳转页面...')

          if (redirect) {
            window.location.replace(decodeURIComponent(redirect))
          } else {
            window.location.replace('/')
          }
        } else {
          toast.error(res.message)
        }
      } catch (error) {
        console.error(error)
        toast.error('登录失败，请检查邮箱和密码是否正确')
      } finally {
        set({ loading: false })
      }
    }
  }),
    {
      name: 'common',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
