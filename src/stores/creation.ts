import { create } from 'zustand';
import { toast } from 'sonner';
import { calculateRatio, creationScrollToBottom, ImageCommandParser, webapi } from '@/lib/utils';
import { unique, shake } from 'radash'
import customParseFormat from 'dayjs/plugin/customParseFormat' // ES 2015
import dayjs from "dayjs";
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');
dayjs.extend(customParseFormat);

export type CreationItemType = {
  id: string;
  prompt: string;
  image: string;
}
export const DEFAULT_ASPECT_RATIO = '1:1';

/**
 * 0: 新创建
 * 1: 执行中
 * 2: 执行成功
 * 3: 失败
 */
export enum DiffusionStatus {
  NEW = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3,
}

/**
 * 0: 默认
 * 1: 变化
 * 2: 高清
 * 3: 重塑
 * 4: 延展
 * 5: 缩放
 * 6: 移除背景
 * 7: 视频生成
 * 8: 视频延长
 * 9: 视频高清
 * 10: 文章生成
 */
export enum JobType {
  DEFAULT = 0,
  VARIATION = 1,
  UPSCALE = 2,
  REMIX = 3,
  PAN = 4,
  OUTPAINT = 5,
  REMOVE_BACKGROUND = 6,
  VIDEO_GENERATION = 7,
  VIDEO_EXTEND = 8,
  VIDEO_UPSCALE = 9,
  ARTICLE_GENERATION = 10,
}

export type JobItemType = {
  id: number;
  job_id: string;
  text: string;
  status: DiffusionStatus;
  type: JobType;
  typeValue?: number | string;
  seed?: string;
  comment?: string;
  urls?: string[];
  created_at?: string;
  updated_at?: string;
  is_copyright?: number;
  basemap?: BasemapType[];
}

export type VCGImageType = {
  id: number;
  title: string;
  url: string;
  width: number;
  height: number;
}

export type VCGImageDataItemType = {
  id: number;
  title: string;
  oss800: string;
  picWidth: number;
  picHeight: number;
}

type UpdateJobItemType = {
  status: DiffusionStatus;
  seed?: string;
  comment?: string;
  urls?: string[];
  updatedAt?: string;
}

export type DiffusionDataType = {
  incr_id: number;
  id: string;
  text: string;
  status: DiffusionStatus;
  seed: string;
  type: JobType;
  created_at: string;
  updated_at: string;
  is_copyright: number;
  basemap: BasemapType[];
  comment: string;
  urls: string[];
}

export type ResponseType<T = unknown> = {
  status_code: number;
  message: string;
  data: T;
}

export type VIDEO_EXTEND_TYPE_NAME = 'video_extend';
export type VIDEO_FIRST_FRAME_TYPE_NAME = 'video_first_frame';

export type BasemapImageClassType = 'sref' | 'cref' | 'content' // 风格、人脸、内容

export type BasemapVideoClassType = VIDEO_EXTEND_TYPE_NAME | VIDEO_FIRST_FRAME_TYPE_NAME;

export type motionType = 'low' | 'high';

export type GenerationType = 'image' | 'video' | 'article';

export interface BasemapBaseType {
  url: string;
  is_copyright: number;
}

interface BasemapImageType extends BasemapBaseType {
  type: BasemapImageClassType;
  width?: number;
  height?: number;
}

export interface VCGImageBasemapType extends BasemapImageType {
  vcgId: number;
}

export interface MJImageBasemapType extends BasemapImageType {
  jobId: string;
  imageNo: number;
}

export interface ImageRemixBasemapType extends BasemapBaseType {
  type: 'remix';
  jobId: string;
  imageNo: number;
  mode: number;
}

interface BasemapVideoType extends BasemapBaseType {
  width?: number;
  height?: number;
}

export interface VideoExtendBasemapType extends BasemapVideoType {
  type: VIDEO_EXTEND_TYPE_NAME;
  jobId: string;
  videoNo: number;
  motion: motionType;
}

export interface VideoGenerationBasemapImageType extends BasemapVideoType {
  type: VIDEO_FIRST_FRAME_TYPE_NAME;
}

export interface VideoGenerationVCGBasemapType extends VideoGenerationBasemapImageType {
  vcgId: number;
}

export interface VideoGenerationMJBasemapType extends VideoGenerationBasemapImageType {
  jobId: string;
  imageNo: number;
}

export const ARTICLE_TYPE_LIST = [
  {
    value: '营销文案',
    label: '营销文案',
  }
];

export const ARTICLE_LENGTH_LIST = [
  {
    value: '100 字',
    label: '短',
  },
  {
    value: '400 字',
    label: '中',
  },
  {
    value: '800 字',
    label: '长',
  }
];

export const ARTICLE_TONE_LIST = [
  {
    value: '专业严谨',
    label: '专业严谨',
  },
  {
    value: '亲切友好',
    label: '亲切友好',
  },
  {
    value: '激励说服',
    label: '激励说服',
  },
  {
    value: '风趣幽默',
    label: '风趣幽默',
  },
  {
    value: '清晰直接',
    label: '清晰直接',
  },
];

export interface ArticleGenerationBasemapType {
  class?: string;
  lenght?: string;
  tone?: string;
}

export const ARTICLE_DEFAULT_DATA: ArticleGenerationBasemapType = {
  class: '营销文案',
  lenght: '400 字',
  tone: '专业严谨',
};

export type BasemapType = VCGImageBasemapType | MJImageBasemapType | ImageRemixBasemapType | VideoExtendBasemapType | VideoGenerationVCGBasemapType | VideoGenerationMJBasemapType;

export type VideoBasemapType = VideoExtendBasemapType | VideoGenerationVCGBasemapType | VideoGenerationMJBasemapType;

export type VideoGenerationParameter = VideoGenerationVCGBasemapType | VideoGenerationMJBasemapType;

type State = {
  prompt: string;
  aspectRatio: string;
  basemap: BasemapType[];
  jobList: JobItemType[];

  searchQuery: string;
  searchResults: VCGImageType[];
  searchLoading: boolean;
  promptLoading: boolean;
  generationLoading: boolean;
  jobTotal: number;
  jobLoading: boolean;
  serverPullJobTotal: number;
  generationType: GenerationType;
  article: ArticleGenerationBasemapType;
}

type Actions = {
  setPrompt: (prompt: string) => void;
  addJob: (job: JobItemType) => void;
  updateJob: (id: string, job: UpdateJobItemType) => void;
  generate: () => void;
  variation: (jobId: string, imageNo: number, type: number) => void;
  upscale: (jobId: string, imageNo: number, type: number) => void;
  remix: (jobId: string, imageNo: number, type: number) => void;
  pan: (jobId: string, imageNo: number, direction: number, scale?: number) => void;
  outpaint: (jobId: string, imageNo: number, scale: number) => void;
  removeBackground: (imageUrl: string, jobId?: string) => void;
  getLocalJob: (id: string) => JobItemType | undefined;
  getJobInfo: (id: string) => Promise<ResponseType<DiffusionDataType>>;
  setAspectRatio: (aspectRatio: string) => void;
  addBasemap: (basemap: BasemapType[], generationType?: GenerationType) => void;
  setBasemap: (basemap: BasemapType[]) => void;
  removeBasemap: (url: string, type: BasemapImageClassType | 'remix') => void;
  clearBasemap: () => void;
  updateBasemapType: (url: string, type: BasemapImageClassType, newType: BasemapImageClassType) => void;
  jobReroll: (id: string) => void;
  setImageRemixBasemap: (jobId: string, imageNo: number, mode: number) => void;
  clearImageRemixBasemap: () => void;

  setSearchQuery: (searchQuery: string) => void;
  setSearchResults: (searchResults: VCGImageType[]) => void;
  setSearchLoading: (searchLoading: boolean) => void;
  searchImageFn: () => void;
  optimizationPromptFn: () => void;
  getJobHistoryFn: (id?: number, pageSize?: number) => void;
  setGenerationType: (generationType: GenerationType) => void;
  autoExtendVideo: (jobId: string, videoNo: number, motion: motionType) => void;
  videoUpscale: (jobId: string, videoNo: number) => void;
  clearVideoBasemap: () => void;
  setVideoFirstFrame: (parameter: VideoGenerationParameter) => void;
  setVideoExtend: (parameter: VideoExtendBasemapType) => void;
  setArticle: (article: ArticleGenerationBasemapType) => void;
}
export const useCreationStore = create<State & Actions>((set, get) => ({
  prompt: '',
  jobList: [],
  aspectRatio: DEFAULT_ASPECT_RATIO,
  basemap: [],
  setImageRemixBasemap: (jobId, imageNo, mode) => {
    const { jobList } = get();

    if (!jobList.length) {
      toast.error('重塑图片不存在');
      return;
    }
    const job = jobList.find(item => item.job_id === jobId);

    if (!job) {
      toast.error('重塑图片不存在');
      return;
    }
    const {
      urls = [],
      is_copyright = 0,
      text = '',
      basemap = [],
    } = job;
    const newBasemap = unique(basemap.concat([{ type: 'remix', jobId, imageNo, mode, url: urls[imageNo], is_copyright }]), item => item.type);
    set({
      basemap: newBasemap,
      prompt: text,
      generationType: 'image'
    })
    toast.info('已设置重塑对象，请点击“重塑图片”按钮进行重塑')
  },
  clearImageRemixBasemap: () => {
    const { basemap } = get();
    const newBasemap = basemap.filter(item => item.type !== 'remix');
    set({ basemap: newBasemap })
  },
  setPrompt: (prompt) => set({ prompt }),
  setAspectRatio: (aspectRatio) => set({ aspectRatio }),
  getLocalJob: (id) => {
    const { jobList } = get();
    const localeJob = jobList.find(item => item.job_id === id);

    return localeJob;
  },
  addBasemap: (es = []) => {
    const { basemap, generationType, searchQuery, prompt } = get();

    if (generationType !== 'image') {
      set({
        generationType: 'image',
        basemap: []
      });
    }
    set({ basemap: unique(basemap.concat(es), item => `${item.url}-${item.type}`) })

    if (!prompt.trim()) {
      set({ prompt: searchQuery.trim() });
    }
  },
  setBasemap: (basemap) => set({ basemap }),
  clearBasemap: () => set({ basemap: [] }),
  removeBasemap: (url, type) => {
    const { basemap } = get();
    const newBasemap = basemap.filter(item => !(item.url === url && item.type === type));
    set({ basemap: newBasemap })
  },
  updateBasemapType: (url, type, newType) => {
    const { basemap } = get();
    const newBasemap = unique(basemap.map(item => item.url === url && item.type === type ? { ...item, type: newType } : item), item => `${item.url}-${item.type}`);
    set({ basemap: newBasemap })
  },
  addJob: (job) => {
    const { jobList } = get();
    set({ jobList: [...jobList, job] });
    toast.info(`开始生成中...`);
    creationScrollToBottom();
    // 此处使用 react-router 逻辑导航到 /creation
    // navigator(`/creation`);
  },
  generate: async () => {
    const {
      prompt,
      aspectRatio,
      basemap = [],
      addJob,
      getLocalJob,
      searchQuery,
      generationType
    } = get();

    if (!prompt.trim()) {
      toast.error('请输入提示词');
      return;
    }
    const is_copyright = basemap.length && basemap.every(({ is_copyright }) => is_copyright === 1) ? 1 : 0;
    console.info("所有底图数据：", basemap);

    const handleImageGeneration = async () => {
      /**
       * url(内容 多个图片链接 空格分开) <prompt 很多字符串文字> --fast --ar 77:58(图像比例 数字 宽：高) --sref url(风格 多个图片链接 空格分开) --cref url(人脸 多个图片链接 空格分开) --v 6.1(模型版本)
       **/
      const textArr: string[] = []
      const srefUrls = [...new Set(basemap.filter(item => item.type === 'sref').map(item => item.url))].join(' ');
      const crefUrls = [...new Set(basemap.filter(item => item.type === 'cref').map(item => item.url))].join(' ');
      const contentUrls = [...new Set(basemap.filter(item => item.type === 'content').map(item => item.url))].join(' ');

      if (contentUrls) {
        textArr.push(contentUrls);
      }
      textArr.push(prompt);

      // 如果 prompt 中没有 --fast，则添加 --fast
      // if (!prompt.includes('--fast')) {
      //   textArr.push(`--fast`);
      //   jobTextArr.push(`--fast`);
      // }
      let ar = aspectRatio;
      // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
      const arMatch = prompt.match(/--ar\s+(\d+:\d+)/);
      // console.info('arMatch', arMatch);
      if (arMatch) {
        ar = arMatch[1];
      }

      if (!prompt.includes('--ar ')) {
        textArr.push(`--ar ${ar}`);
      }

      if (srefUrls && !prompt.includes('--sref ')) {
        textArr.push(`--sref ${srefUrls}`);
      }

      if (crefUrls && !prompt.includes('--cref ')) {
        textArr.push(`--cref ${crefUrls}`);
      }

      // if (!prompt.includes('--v ')) {
      //   textArr.push(`--v 6.1`);
      //   jobTextArr.push(`--v 6.1`);
      // }

      const text = textArr.join(' ');
      const remixBasemap = basemap.find(item => item.type === 'remix');

      if (remixBasemap) {
        const { jobId, imageNo, mode } = remixBasemap;
        try {
          const res = await webapi.post('creations/v1/remix', {
            json: {
              jobId,
              imageNo,
              mode,
              remixPrompt: text,
              is_copyright,
              basemap,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {};
            const currentJob = getLocalJob(jobId);
            addJob({
              id: incr_id,
              job_id: id,
              type: JobType.REMIX,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text: text || currentJob?.text || '',
              is_copyright,
              basemap,
            });
          }
        } catch (error) {
          console.error(error);
          toast.error('重塑接口调用失败，请稍后重试');
        }
      } else {
        try {
          const res = await webapi.post('creations/v1/diffusion', {
            json: {
              text,
              phrase: searchQuery.trim(),
              is_copyright,
              basemap,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {};
            addJob({
              id: incr_id,
              job_id: id,
              text,
              type: JobType.DEFAULT,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              is_copyright,
              basemap,
            });
          } else {
            toast.error(res.message);
          }
        } catch (error) {
          console.error(error);
          toast.error('图片生成接口调用失败，请稍后重试');
        }
      }
    }

    const handleVideoGeneration = async () => {
      const videoBasemap = basemap.find(item => item.type === 'video_extend' || item.type === 'video_first_frame');

      if (!videoBasemap) {
        toast.error('请设置视频首帧图片');
        return;
      }

      if (videoBasemap.type === 'video_extend') {
        const { jobId, videoNo, motion } = videoBasemap;
        try {
          const res = await webapi.post('creations/v1/extend_video', {
            json: {
              jobId,
              videoNo,
              prompt,
              is_copyright,
              basemap,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {};
            addJob({
              id: incr_id,
              job_id: id,
              type: JobType.VIDEO_EXTEND,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text: prompt,
              typeValue: motion,
              is_copyright,
              basemap,
            });
          } else {
            toast.error(res.message);
          }
        } catch (error) {
          console.error(error);
          toast.error('视频延长接口调用失败，请稍后重试');
        }
      }

      if (videoBasemap.type === 'video_first_frame') {
        const { width, height, url } = videoBasemap;
        const promptArr: string[] = [];

        if (url) {
          promptArr.push(url);
        } else {
          toast.error('请设置视频首帧');
          return;
        }
        promptArr.push(prompt);

        if (width && height) {
          promptArr.push(`--ar ${calculateRatio(width || 1, height || 1)}`);
        }
        const text = promptArr.join(' ');
        try {
          const res = await webapi.post('creations/v1/video_diffusion', {
            json: {
              ...shake({
                prompt: text,
                jobId: '',
                imageNo: 0,
              }, a => !a),
              is_copyright,
              basemap,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {};
            addJob({
              id: incr_id,
              job_id: id,
              type: JobType.VIDEO_GENERATION,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text,
              is_copyright,
              basemap,
            });
          } else {
            toast.error(res.message);
          }
        } catch (error) {
          console.error(error);
          toast.error('视频生成接口调用失败，请稍后重试');
        }
      }
    }

    const handleArticleGeneration = async () => {
      console.info('handleArticleGeneration');
    }

    switch (generationType) {
      case 'video':
        handleVideoGeneration();
        break;
      case 'article':
        handleArticleGeneration();
        break;
      case 'image':
      default:
        handleImageGeneration();
        break;
    }
    creationScrollToBottom();
  },
  variation: async (jobId, imageNo, type) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/variation', {
      json: {
        jobId,
        imageNo,
        type,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.VARIATION,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  upscale: async (jobId, imageNo, type) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/upscale', {
      json: {
        jobId,
        imageNo,
        type,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.UPSCALE,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  remix: async (jobId, imageNo, type) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/remix', {
      json: {
        jobId,
        imageNo,
        type,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.REMIX,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  pan: async (jobId, imageNo, direction, scale = 1.2) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/pan', {
      json: {
        jobId,
        imageNo,
        direction,
        scale,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.PAN,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  outpaint: async (jobId, imageNo, scale = 1.5) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/outpaint', {
      json: {
        jobId,
        imageNo,
        scale,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.OUTPAINT,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  removeBackground: async (imageUrl, jobId) => {
    const { getLocalJob, addJob } = get();
    const res = await webapi.post('creations/v1/remove_background', {
      json: {
        imgUrl: imageUrl,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      const currentJob = getLocalJob(jobId || '');
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.REMOVE_BACKGROUND,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text: currentJob?.text || '',
      });
    }
  },

  getJobInfo: async (id) => {
    const res = await webapi.get("creations/v1/job", {
      searchParams: {
        jobId: id,
      }
    }).json<ResponseType<DiffusionDataType>>();

    return res;
  },
  updateJob: (id, job) => {
    const { jobList } = get();
    set({ jobList: jobList.map(item => item.job_id === id ? { ...item, ...job } : item) });
  },
  jobReroll: async (jobId) => {
    const { addJob, getLocalJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [] } = currentJob || {};
    const res = await webapi.post('creations/v1/reroll', {
      json: {
        jobId,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id: newId, incr_id, text, seed, comment, type } = res.data || {};

      if (!currentJob) {
        toast.error('任务不存在');
        return;
      }
      addJob({
        id: incr_id,
        job_id: newId,
        text,
        type: type || JobType.DEFAULT,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        is_copyright,
        basemap,
      });
    } else {
      toast.error(res.message);
    }
  },
  searchQuery: '',
  searchResults: [],
  searchLoading: false,
  promptLoading: false,
  setSearchQuery: (searchQuery) => set({ searchQuery }),
  setSearchResults: (searchResults) => set({ searchResults }),
  setSearchLoading: (searchLoading) => set({ searchLoading }),
  searchImageFn: async () => {
    const { setSearchResults, setSearchLoading, searchLoading, searchQuery } = get();

    if (searchLoading) {
      return;
    }

    const query = searchQuery.trim();

    if (!query) {
      toast.error('请输入搜索内容');
      return;
    }
    setSearchLoading(true);
    try {
      const imagesRes = await webapi.post('search/image', {
        json: {
          text: query,
        }
      }).json<ResponseType<VCGImageDataItemType[]>>();

      if (imagesRes.status_code !== 1) {
        toast.error('图库获取图片信息接口调用失败，请稍后重试');
        return;
      }
      const images = imagesRes.data.map(image => {
        const { id, title, oss800, picWidth, picHeight } = image;

        return ({
          id,
          title,
          url: oss800,
          width: picWidth,
          height: picHeight
        });
      });
      setSearchResults(images);
    } catch (error) {
      console.error(error);
      setSearchResults([]);
      toast.error('图库搜索接口调用失败，请稍后重试');
    } finally {
      setSearchLoading(false);
    }
  },
  optimizationPromptFn: async () => {
    const { prompt, setPrompt, promptLoading } = get();

    if (promptLoading) {
      toast.info('正在优化提示词，请稍后...');
      return;
    }
    const query = prompt.trim();

    if (!query) {
      toast.error('请输入需要生成的内容');
      return;
    }

    set({ promptLoading: true });
    try {
      const res = await webapi.post('prompt', {
        json: {
          data: query,
        }
      }).json<ResponseType<string>>();

      if (res.status_code === 1) {
        setPrompt(res.data);
        // toast.success('提示词优化完成');
      } else {
        toast.error(res.message);
      }
    } catch (error) {
      console.error(error);
      toast.error('优化提示词接口调用失败，请稍后重试');
    } finally {
      set({ promptLoading: false });
    }
  },
  generationLoading: false,
  jobTotal: 0,
  serverPullJobTotal: 0,
  jobLoading: false,
  getJobHistoryFn: async (id, pageSize = 10) => {
    const { jobLoading, serverPullJobTotal = 0 } = get();

    if (jobLoading) {
      return;
    }
    set({ jobLoading: true });
    const toastId = toast.loading('正在获取任务历史...');
    try {
      const res = await webapi.get('my/jobs', {
        searchParams: shake({
          offset_id: id,
          page_size: pageSize,
        })
      }).json<ResponseType<{
        list: JobItemType[];
        total: number;
      }>>();

      if (res.status_code === 1) {
        const { jobList: oldJobList } = get();
        const { list, total = 0 } = res.data || {};
        const len = list.length || 0;

        if (len) {
          set({ jobList: [...(list.reverse() || []), ...oldJobList], jobTotal: total, serverPullJobTotal: serverPullJobTotal + len });
        } else {
          toast.info('没有更多任务了');
        }
      }
    } catch (error) {
      console.error(error);
      toast.error('获取任务历史记录失败，请重试');
    } finally {
      set({ jobLoading: false });

      if (toastId) {
        setTimeout(() => {
          toast.dismiss(toastId);
        }, 300);
      }
    }
  },
  generationType: 'image',
  setGenerationType: (generationType) => set({ generationType }),
  autoExtendVideo: async (jobId, videoNo, motion) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    if (!currentJob) {
      toast.error('任务不存在');
      return;
    }
    const { text, is_copyright = 0, basemap = [] } = currentJob;
    const { data } = ImageCommandParser.parse(text);
    const prompt = (data?.prompt || text) + ' --motion ' + motion;
    try {
      const res = await webapi.post('creations/v1/extend_video', {
        json: {
          jobId,
          videoNo,
          prompt,
          is_copyright,
          basemap,
        }
      }).json<ResponseType<DiffusionDataType>>();

      if (res.status_code === 1) {
        const { id, seed, comment, incr_id } = res.data || {};
        addJob({
          id: incr_id,
          job_id: id,
          type: JobType.VIDEO_EXTEND,
          status: DiffusionStatus.PROCESSING,
          created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          seed,
          comment,
          text: text + ' --motion ' + motion,
          typeValue: motion,
          is_copyright,
          basemap,
        });
      } else {
        toast.error(res.message);
      }
    } catch (error) {
      console.error(error);
      toast.error('视频延长接口调用失败，请稍后重试');
    }
  },
  videoUpscale: async (jobId, videoNo) => {
    const { getLocalJob, addJob } = get();
    const currentJob = getLocalJob(jobId);
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {};
    const res = await webapi.post('creations/v1/video_upscale', {
      json: {
        jobId,
        videoNo,
        is_copyright,
        basemap,
      }
    }).json<ResponseType<DiffusionDataType>>();

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {};
      addJob({
        id: incr_id,
        job_id: id,
        type: JobType.VIDEO_UPSCALE,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      });
    }
  },
  clearVideoBasemap: () => {
    const { basemap } = get();

    if (basemap) {
      const newBasemap = basemap.filter((item) => item.type !== 'video_extend' && item.type !== 'video_first_frame') || [];
      set({ basemap: newBasemap });
    }
  },
  setVideoFirstFrame: (parameter) => {
    set({
      basemap: [parameter],
      generationType: 'video'
    });
  },
  setVideoExtend(parameter) {
    set({
      basemap: [parameter],
      generationType: 'video'
    })
  },
  setArticle
}));
