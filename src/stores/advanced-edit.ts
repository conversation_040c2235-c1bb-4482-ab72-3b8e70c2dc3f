import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware'
import { toast } from 'sonner';
import { blobToBase64, imageCompose, indexedDBStorage, webapi, uploadImage } from '@/lib/utils';
import { unique } from 'radash'
import { ImageLayer, LayerType } from './edit';
import { DiffusionStatus } from './creation';
import { saveAs } from 'file-saver';
import ky from 'ky';
import { API_URL } from '@/config';

export type CreationItemType = {
  id: string;
  prompt: string;
  image: string;
}
export const DEFAULT_ASPECT_RATIO = '1:1';
export type JobItemType = {
  id: string;
  prompt: string;
  status: DiffusionStatus;
  loading: boolean;
  createdAt: string;
  type: ActiveType;
  updatedAt?: string;
  aspectRatio?: string;
  data?: DiffusionDataType;
  seed?: number;
  comment?: string;
  establishingShot: EstablishingShotType[];
  activeType: ActiveType;
  worktop: WorktopType;
  foregrounds: LayerType[];
  currentForeground: LayerType | null;
  cover: string;
}

type UpdateJobItemType = {
  id: string;
  status: JobItemType['status'];
  data?: DiffusionDataType;
  loading?: boolean;
  updatedAt?: string;
}

type DiffusionDataType = {
  id: string;
  text: string;
  status: DiffusionStatus;
  seed: number;
  comment: string;
  urls: string[];
}

type ResponseType<T = unknown> = {
  status_code: number;
  message: string;
  data: T;
}

export type EstablishingShotClassType = 'sref' | 'cref' | 'content' // 风格、人脸、内容

export type EstablishingShotType = {
  id: string;
  name?: string;
  url: string;
  type: EstablishingShotClassType;
}

export type ActiveType = 'edit' | 'retexture'

export type OperationType = 'move' | 'erase' | 'reverseErase'

export type WorktopType = {
  width: number;
  height: number;
}

type State = {
  activeType: ActiveType;
  operationType: OperationType;
  prompt: string;
  aspectRatio: string;
  establishingShot: EstablishingShotType[];
  jobList: JobItemType[];
  worktop: WorktopType | null;
  foregrounds: LayerType[];
  currentForeground: LayerType | null;
  isGenerating: boolean;
  canDownload: boolean;
  brushSize: number;
}

type Actions = {
  setActiveType: (activeType: ActiveType) => void;
  setOperationType: (operationType: OperationType) => void;
  setWorktop: (worktop: WorktopType | null) => void;
  setCurrentForeground: (currentForeground: LayerType | null) => void;
  addForeground: (foreground: LayerType) => void;
  removeForeground: () => void;
  updateForeground: (id: number, foreground: Partial<LayerType>) => void;
  moveForegroundUp: () => void;
  moveForegroundDown: () => void;
  reorderForegrounds: (fromIndex: number, toIndex: number) => void;
  clearForegrounds: () => void;
  setPrompt: (prompt: string) => void;
  addJob: (job: JobItemType) => void;
  updateJob: (id: string, job: UpdateJobItemType) => void;
  generate: () => void;
  retexture: () => void;
  variation: (jobId: string, imageNo: number, type: number) => void;
  upscale: (jobId: string, imageNo: number, type: number) => void;
  remix: (jobId: string, imageNo: number, type: number) => void;
  pan: (jobId: string, imageNo: number, direction: number, scale?: number) => void;
  outpaint: (jobId: string, imageNo: number, scale: number) => void;
  removeBackground: (imageUrl: string, jobId?: string) => void;
  getLocalJob: (id: string) => JobItemType | undefined;
  getJobInfo: (id: string) => Promise<ResponseType<DiffusionDataType>>;
  setAspectRatio: (aspectRatio: string) => void;
  addEstablishingShot: (establishingShot: EstablishingShotType[]) => void;
  setEstablishingShot: (establishingShot: EstablishingShotType[]) => void;
  removeEstablishingShot: (id: string, type: EstablishingShotClassType) => void;
  clearEstablishingShot: () => void;
  updateEstablishingShotType: (id: string, type: EstablishingShotClassType, newType: EstablishingShotClassType) => void;
  jobReroll: (id: string) => void;
  viewJobImage: (id: string, imageUrl?: string) => void;
  downloadJobImage: () => void;
  suggestPrompt: () => void;
  autoRemoveBackground: () => void;
  removeJob: (id: string) => void;
  setBrushSize: (brushSize: number) => void;
}

export const useAdvancedEditStore = create<State & Actions>()(
  persist((set, get) => ({
    activeType: 'edit',
    operationType: 'move',
    prompt: '',
    aspectRatio: DEFAULT_ASPECT_RATIO,
    establishingShot: [],
    jobList: [],
    worktop: null,
    foregrounds: [],
    currentForeground: null,
    isGenerating: false,
    canDownload: false,
    brushSize: 50,
    setBrushSize: (brushSize) => set({ brushSize }),
    setWorktop: (worktop) => set({ worktop }),
    setOperationType: (operationType) => set({ operationType }),
    addForeground: (foreground) => set({ foregrounds: [...get().foregrounds, foreground], currentForeground: foreground }),
    removeForeground: () => {
      const { foregrounds, currentForeground } = get();
      if (currentForeground) {
        set({ foregrounds: foregrounds.filter(item => item.id !== currentForeground.id), currentForeground: null });
      }
    },
    updateForeground: (id, foreground) => {
      const { foregrounds } = get();
      // 优化更新方式，避免不必要的映射操作
      const foregroundIndex = foregrounds.findIndex(item => item.id === id);
      if (foregroundIndex !== -1) {
        const newForegrounds = [...foregrounds];
        const current = newForegrounds[foregroundIndex];
        newForegrounds[foregroundIndex] = {
          ...current,
          ...foreground,
        } as LayerType;
        set({
          foregrounds: newForegrounds,
          canDownload: false,
        });
      } else {
        set({ canDownload: false });
      }
    },
    moveForegroundUp: () => {
      const { foregrounds, currentForeground } = get();
      if (currentForeground) {
        const index = foregrounds.findIndex(item => item.id === currentForeground.id);
        if (index > 0) {
          const newForegrounds = [...foregrounds];
          [newForegrounds[index], newForegrounds[index - 1]] = [newForegrounds[index - 1], newForegrounds[index]];
          set({ foregrounds: newForegrounds, currentForeground: newForegrounds[index - 1] });
        }
      }
    },
    moveForegroundDown: () => {
      const { foregrounds, currentForeground } = get();
      if (currentForeground) {
        const index = foregrounds.findIndex(item => item.id === currentForeground.id);
        if (index < foregrounds.length - 1) {
          const newForegrounds = [...foregrounds];
          [newForegrounds[index], newForegrounds[index + 1]] = [newForegrounds[index + 1], newForegrounds[index]];
          set({ foregrounds: newForegrounds, currentForeground: newForegrounds[index + 1] });
        }
      }
    },
    reorderForegrounds: (fromIndex, toIndex) => {
      const { foregrounds } = get();
      if (fromIndex === toIndex || fromIndex < 0 || toIndex < 0 || fromIndex >= foregrounds.length || toIndex >= foregrounds.length) {
        return;
      }
      
      const newForegrounds = [...foregrounds];
      const [moved] = newForegrounds.splice(fromIndex, 1);
      newForegrounds.splice(toIndex, 0, moved);
      
      set({ foregrounds: newForegrounds });
    },
    clearForegrounds: () => set({ foregrounds: [], currentForeground: null }),
    setCurrentForeground: (currentForeground) => set({ currentForeground }),
    setPrompt: (prompt) => set({ prompt }),
    setAspectRatio: (aspectRatio) => set({ aspectRatio }),
    setActiveType: (activeType) => set({ activeType }),
    getLocalJob: (id) => {
      const { jobList } = get();
      return jobList.find(item => item.id === id);
    },
    addEstablishingShot: (es) => {
      const { establishingShot } = get();

      // if (establishingShot.find(item => item.url === es.url && item.type === es.type)) {
      //   return;
      // }
      // 需要对比两个字段
      const newEstablishingShot = unique(establishingShot.concat(es), item => `${item.url}-${item.type}`);
      set({ establishingShot: newEstablishingShot })
      toast.info(`添加成功`)
    },
    setEstablishingShot: (establishingShot) => set({ establishingShot }),
    clearEstablishingShot: () => set({ establishingShot: [] }),
    removeEstablishingShot: (id, type) => {
      const { establishingShot } = get();
      const newEstablishingShot = establishingShot.filter(item => !(item.id === id && item.type === type));
      set({ establishingShot: newEstablishingShot })
      toast.info(`删除成功`)
    },
    updateEstablishingShotType: (id, type, newType) => {
      const { establishingShot } = get();
      const newEstablishingShot = unique(establishingShot.map(item => item.id === id && item.type === type ? { ...item, type: newType } : item), item => `${item.url}-${item.type}`);
      set({ establishingShot: newEstablishingShot })
    },
    addJob: (job) => {
      const { jobList } = get();
      set({ jobList: [job, ...jobList] });
      toast.info(`开始生成中...`);
    },
    generate: async () => {
      const {
        prompt,
        aspectRatio,
        establishingShot,
        addJob,
        worktop,
        setCurrentForeground,
        currentForeground,
        foregrounds,
      } = get();
      if (!worktop) {
        toast.error('请先上传图片');
        return;
      }

      if (prompt.trim()) {
        try {

          console.info('generate', prompt);
          setCurrentForeground(null);
          set({ isGenerating: true });
          /**
           * url(内容 多个链接，空格分开) <prompt> --fast --ar 77:58(图像比例，宽：高) --sref url(风格 多个链接，空格分开) --cref url(人脸 多个链接，空格分开) --v 6.1(模型版本)
           *  */
          const textArr: string[] = []
          const jobTextArr: string[] = []
          const srefUrls = [...new Set(establishingShot.filter(item => item.type === 'sref').map(item => item.url))].join(' ');
          const crefUrls = [...new Set(establishingShot.filter(item => item.type === 'cref').map(item => item.url))].join(' ');
          const contentUrls = [...new Set(establishingShot.filter(item => item.type === 'content').map(item => item.url))].join(' ');

          if (contentUrls) {
            textArr.push(contentUrls);
          }
          textArr.push(prompt);
          jobTextArr.push(prompt);

          // 如果 prompt 中没有 --fast，则添加 --fast
          // if (!prompt.includes('--fast')) {
          //   textArr.push(`--fast`);
          //   jobTextArr.push(`--fast`);
          // }
          // let ar = aspectRatio;
          // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
          // const arMatch = prompt.match(/--ar\s+(\d+:\d+)/);
          // console.info('arMatch', arMatch);
          // if (arMatch) {
          //   ar = arMatch[1];
          // }

          // if (!prompt.includes('--ar ')) {
          //   textArr.push(`--ar ${ar}`);
          //   jobTextArr.push(`--ar ${ar}`);
          // }

          if (srefUrls && !prompt.includes('--sref ')) {
            textArr.push(`--sref ${srefUrls}`);
          }

          if (crefUrls && !prompt.includes('--cref ')) {
            textArr.push(`--cref ${crefUrls}`);
          }

          if (!prompt.includes('--v ')) {
            textArr.push(`--v 6.1`);
            jobTextArr.push(`--v 6.1`);
          }

          const text = textArr.join(' ');
          const jobText = jobTextArr.join(' ');
          const { width, height } = worktop;
          const { blackAndWhiteBlob, blob } = await imageCompose('advanced-edit', width, height);
          const maskUrl = await uploadImage(new File([blackAndWhiteBlob], 'mask.png', { type: 'image/png' }));
          const imgUrl = await uploadImage(new File([blob], 'image.png', { type: 'image/png' }));
          const res = await webapi.post('upload-paint', {
            json: {
              remixPrompt: text,
              mask: {
                url: maskUrl,
              },
              canvas: {
                width,
                height,
              },
              imgPos: {
                x: 0,
                y: 0,
                width,
                height,
              },
              imgUrl,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment } = res.data || {};
            addJob({
              id,
              prompt: jobText,
              loading: true,
              type: 'edit',
              status: DiffusionStatus.PROCESSING,
              createdAt: new Date().toISOString(),
              seed,
              comment,
              // aspectRatio: ar,
              aspectRatio,
              establishingShot,
              activeType: 'edit',
              worktop,
              foregrounds,
              currentForeground,
              cover: imgUrl + '',
            });
          } else {
            toast.error(res.message);
          }
          set({ isGenerating: false });
        } catch (error) {
          set({ isGenerating: false });
          toast.error("生成失败，请重试")
          console.error('generate error', error);
        }
      }
    },
    suggestPrompt: async () => {
      const {
        worktop,
        foregrounds
      } = get();

      if (!foregrounds.length) {
        toast.error('请先上传图片');
        return;
      }

      if (!worktop) {
        toast.error('请先上传图片');
        return;
      }
      set({ currentForeground: null });
      const { width, height } = worktop;
      // 缩小图片尺寸，保持原始宽高比，最长边为 800px
      // const maxSize = 800;
      // const ratio = width / height;
      // if (width > height) {
      //   if (width > maxSize) {
      //     width = maxSize;
      //     height = width / ratio;
      //   }
      // } else {
      //   if (height > maxSize) {
      //     height = maxSize;
      //     width = height * ratio;
      //   }
      // }
      const { base64 } = await imageCompose('advanced-edit', width, height, '#ffffff');
      // 需要把 base64 图片的尺寸缩小到最长边为 800px
      const res = await ky.post('http://localhost:11434/api/generate', {
        json: {
          model: 'gemma3:27b-it-qat',
          prompt: '请使用中文描述这张图片的主体结构和大致内容，用于 Midjourney 生成图片的 prompt，不要包含任何无关的描述，直接返回描述内容即可',
          images: [base64.split(',')[1]],
          stream: false,
          options: {
            temperature: 0.1, // 降低随机性，提高精确度
            num_predict: 1024, // 允许足够长的回复
          }
        },
        timeout: 120000,
      }).json<{
        response: string;
        done: boolean;
      }>();
      if (res.done) {
        set({ prompt: res.response });
      } else {
        toast.error('生成失败');
      }
    },
    autoRemoveBackground: async () => {
      const { currentForeground, updateForeground } = get();
      if (!currentForeground || currentForeground.type !== 'image') {
        toast.error('请先选择一个图片图层');
        return;
      }
      const { id, base64 } = currentForeground;

      if (!base64) {
        toast.error('请先选择一个图片图层');
        return;
      }
      let b64 = base64;
      if (base64.indexOf('http') === 0) {
        // 此处需要把网址转为 base64
        const res = await ky.get(base64).blob();
        // console.info('res', res);
        b64 = await blobToBase64(res);
      }
      const res = await ky.post(`${API_URL}/aigc/cutout`, {
        json: {
          image: b64.split(",")[1],
        },
        timeout: 180000,
      }).json<{
        data: {
          image: string,
        },
        status: number,
        message: string,
      }>();

      if (res.data.image === 'ZXJyb3I=') {
        toast.error("所选图层没有合适的主体，请选择其它图层");
        return;
      }
      updateForeground(id, {
        rmbgBase64: `data:image/png;base64,${res.data.image}`,
      });
    },
    viewJobImage: (id, imageUrl) => {
      const { jobList } = get();
      const job = jobList.find(item => item.id === id);

      if (job) {
        const {
          worktop,
          activeType,
          prompt,
          aspectRatio,
          establishingShot,
          foregrounds,
          currentForeground,
        } = job;
        const current: LayerType[] = imageUrl ? [{
          id: Date.now(),
          type: 'image',
          aspectRatio: worktop.width / worktop.height,
          width: worktop.width,
          height: worktop.height,
          base64: imageUrl,
          x: 0,
          y: 0,
          rotate: 0,
        }] : foregrounds;
        set({
          canDownload: imageUrl ? true : false,
          worktop,
          foregrounds: current,
          operationType: 'move',
          currentForeground: imageUrl ? current[0] : currentForeground,
          activeType,
          prompt,
          aspectRatio,
          establishingShot,
        })
      }
    },
    downloadJobImage: async () => {
      const { foregrounds } = get();
      const imageLayer = foregrounds?.[0] as ImageLayer;

      if (imageLayer?.base64) {
        await saveAs(imageLayer.base64, 'image.png');
        toast.success('下载成功');
      } else {
        toast.error('请先上传图片');
      }
    },
    retexture: async () => {
      const {
        prompt,
        aspectRatio,
        establishingShot,
        addJob,
        worktop,
        setCurrentForeground,
        foregrounds,
      } = get();

      if (!worktop) {
        toast.error('请先上传图片');
        return;
      }

      if (prompt.trim()) {
        try {
          console.info('retexture', prompt);
          setCurrentForeground(null);
          set({ isGenerating: true });
          /**
           * url(内容 多个链接，空格分开) <prompt> --fast --ar 77:58(图像比例，宽：高) --sref url(风格 多个链接，空格分开) --cref url(人脸 多个链接，空格分开) --v 6.1(模型版本)
           *  */
          const textArr: string[] = []
          const jobTextArr: string[] = []
          const srefUrls = [...new Set(establishingShot.filter(item => item.type === 'sref').map(item => item.url))].join(' ');
          const crefUrls = [...new Set(establishingShot.filter(item => item.type === 'cref').map(item => item.url))].join(' ');
          const contentUrls = [...new Set(establishingShot.filter(item => item.type === 'content').map(item => item.url))].join(' ');

          if (contentUrls) {
            textArr.push(contentUrls);
          }
          textArr.push(prompt);
          jobTextArr.push(prompt);

          // 如果 prompt 中没有 --fast，则添加 --fast
          if (!prompt.includes('--fast')) {
            textArr.push(`--fast`);
            jobTextArr.push(`--fast`);
          }
          // let ar = aspectRatio;
          // // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
          // const arMatch = prompt.match(/--ar\s+(\d+:\d+)/);
          // // console.info('arMatch', arMatch);
          // if (arMatch) {
          //   ar = arMatch[1];
          // }

          // if (!prompt.includes('--ar ')) {
          //   textArr.push(`--ar ${ar}`);
          //   jobTextArr.push(`--ar ${ar}`);
          // }

          if (srefUrls && !prompt.includes('--sref ')) {
            textArr.push(`--sref ${srefUrls}`);
          }

          if (crefUrls && !prompt.includes('--cref ')) {
            textArr.push(`--cref ${crefUrls}`);
          }

          if (!prompt.includes('--v ')) {
            textArr.push(`--v 6.1`);
            jobTextArr.push(`--v 6.1`);
          }

          const text = textArr.join(' ');
          const jobText = jobTextArr.join(' ');
          const { width, height } = worktop;
          const { blob } = await imageCompose('advanced-edit', width, height, '#ffffff');
          const imgUrl = await uploadImage(new File([blob], 'image.png', { type: 'image/png' }));
          const res = await webapi.post('retexture', {
            json: {
              remixPrompt: text,
              imgUrl,
            }
          }).json<ResponseType<DiffusionDataType>>();

          if (res.status_code === 1) {
            const { id, seed, comment } = res.data || {};
            addJob({
              id,
              prompt: jobText,
              loading: true,
              type: 'retexture',
              status: DiffusionStatus.PROCESSING,
              createdAt: new Date().toISOString(),
              seed,
              comment,
              aspectRatio,
              establishingShot,
              activeType: 'retexture',
              worktop,
              foregrounds,
              currentForeground: null,
              cover: imgUrl + '',
            });
          } else {
            toast.error(res.message);
          }
          set({ isGenerating: false });
        } catch (error) {
          set({ isGenerating: false });
          toast.error("生成失败，请重试")
          console.error('generate error', error);
        }
      }
    },
    variation: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('variation', {
      //   json: {
      //     jobId,
      //     imageNo,
      //     type,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId);
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },
    upscale: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('upscale', {
      //   json: {
      //     jobId,
      //     imageNo,
      //     type,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId);
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },
    remix: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('remix', {
      //   json: {
      //     jobId,
      //     imageNo,
      //     type,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId);
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },
    pan: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('pan', {
      //   json: {
      //     jobId,
      //     imageNo,
      //     direction,
      //     scale,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId);
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },
    outpaint: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('outpaint', {
      //   json: {
      //     jobId,
      //     imageNo,
      //     scale,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId);
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },
    removeBackground: async () => {
      // const { getLocalJob, addJob } = get();
      // const res = await webapi.post('remove_background', {
      //   json: {
      //     imgUrl: imageUrl,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id, seed, comment } = res.data || {};
      //   const currentJob = getLocalJob(jobId || '');
      //   addJob({
      //     id,
      //     type: 'edit',
      //     status: DiffusionStatus.PROCESSING,
      //     loading: true,
      //     createdAt: new Date().toISOString(),
      //     seed,
      //     comment,
      //     prompt: currentJob?.prompt || '',
      //     aspectRatio: currentJob?.aspectRatio || DEFAULT_ASPECT_RATIO,
      //     establishingShot: currentJob?.establishingShot || [],
      //   });
      // }
    },

    getJobInfo: async (id) => {
      const res = await webapi.get("job", {
        searchParams: {
          jobId: id,
        }
      }).json<ResponseType<DiffusionDataType>>();

      return res;
    },
    updateJob: (id, job) => {
      const { jobList } = get();
      set({ jobList: jobList.map(item => item.id === id ? { ...item, ...job } : item) });
    },
    jobReroll: async () => {
      // const { addJob, getLocalJob } = get();
      // const res = await webapi.post('reroll', {
      //   json: {
      //     jobId,
      //   }
      // }).json<ResponseType<DiffusionDataType>>();

      // if (res.status_code === 1) {
      //   const { id: newId } = res.data || {};
      //   const currentJob = getLocalJob(jobId || '');

      //   if (!currentJob) {
      //     toast.error('任务不存在');
      //     return;
      //   }
      //   // updateJob(id, {
      //   //   id: newId,
      //   //   status: DiffusionStatus.PROCESSING,
      //   //   loading: true,
      //   //   updatedAt: new Date().toISOString(),
      //   //   data: undefined
      //   // });
      //   const { prompt, aspectRatio, establishingShot, seed, comment, type } = currentJob;
      // addJob({
      //   id: newId,
      //   prompt,
      //   loading: true,
      //   type: type || 'edit',
      //   status: DiffusionStatus.PROCESSING,
      //   createdAt: new Date().toISOString(),
      //   seed,
      //   comment,
      //   aspectRatio,
      //   establishingShot,
      // });
      // } else {
      //   toast.error(res.message);
      // }
    },
    removeJob: (id) => {
      const { jobList } = get();
      set({ jobList: jobList.filter(item => item.id !== id) });
    }
  }),
    {
      name: 'advanced-edit',
      storage: createJSONStorage(() => indexedDBStorage),
    })
);
