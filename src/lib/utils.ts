import { API_URL, ImageInfoType } from "@/config";
import { clsx, type ClassValue } from "clsx"
import ky from "ky";
import { toast } from "sonner";
import { twMerge } from "tailwind-merge";
import domtoimage from 'dom-to-image';
import { StateStorage } from "zustand/middleware";
import { get, set, del } from "idb-keyval";
import { GenerationType, JobType } from "@/stores/creation";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function handle401Redirect() {
  // 为了防止重定向循环，如果已在登录页则不执行
  if (window.location.pathname === '/login') {
    return;
  }
  const currentRoute = window.location.pathname + window.location.search;
  toast.error('登录已过期，请重新登录');
  window.location.replace(`/login?redirect=${encodeURIComponent(currentRoute)}`);
}

// 判断一个图片是否有透明部分
export function hasTransparentPart(file: File): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = function () {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        resolve(false);
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      for (let i = 0; i < data.length; i += 4) {
        if (data[i + 3] < 255) {
          resolve(true);
          return;
        }
      }

      resolve(false);
    };

    img.onerror = function () {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}

export const fileToBase64 = (file: File): Promise<string | ArrayBuffer | null> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(file);
  });
}

export const base64ToBlob = (base64: string, contentType = 'image/jpeg'): Blob => {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: contentType });
}

export const getToken = async (enforce: boolean = false) => {
  let token = localStorage.getItem('token');

  if (!token || enforce) {
    const res = await ky.get(`${API_URL}/get/token?key=014adefd106f009cb1998549b64d9cfc`).json<{
      access_token: string;
    }>();
    localStorage.setItem('token', res.access_token);
    token = res.access_token;
  }

  return token;
}

export const getLocalToken = () => localStorage.getItem('t') || '';

export const setLocalToken = (token: string) => localStorage.setItem('t', token);


export const webapi = ky.extend({
  prefixUrl: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        const token = getLocalToken();
        request.headers.set('Authorization', `Bearer ${token}`);
      },
    ],
    afterResponse: [
      async (_request, _options, response) => {
        if (response.status === 401) {
          handle401Redirect();
        }

        return response;
      }
    ]
  }
})

export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(blob);
  });
}

export const urlToBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url);
  const blob = await response.blob();
  return await blobToBase64(blob);
}

export const getImageInfoByUrl = async (url: string): Promise<ImageInfoType> => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = async function () {
      let hasTransparentPart = false;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        hasTransparentPart = false;
      } else {
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.drawImage(image, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          if (data[i + 3] < 255) {
            hasTransparentPart = true;
            break;
          }
        }
      }

      const base64 = await urlToBase64(url);
      console.info('base64', base64);
      resolve({
        width: image.width,
        height: image.height,
        base64: base64 as string,
        hasTransparentPart,
      });
    };

    image.onerror = function () {
      toast.error('图片加载失败，请检查图片地址是否正确');
      reject(new Error('Failed to load image'));
    };

    image.src = url;
  });
}

/**
 * 使用欧几里得算法计算两个非负整数的最大公约数 (GCD)。
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns a 和 b 的最大公约数
 */
export function calculateGCD(a: number, b: number): number {
  // 确保处理的是非负整数
  a = Math.abs(Math.round(a));
  b = Math.abs(Math.round(b));

  // 欧几里得算法
  while (b !== 0) {
    const temp = b;
    b = a % b;
    a = temp;
  }
  return a;
}

/**
 * 计算两个数的简化比率 (x:y)。
 * @param x 比率的第一个数
 * @param y 比率的第二个数
 * @returns 格式化为 "simplifiedX:simplifiedY" 的字符串，或在 y 为 0 时抛出错误。
 */
export function calculateRatio(x: number, y: number): string {
  if (y === 0) {
    return "";
  }

  if (x === 0) {
    return "";
  }

  // 计算最大公约数
  const commonDivisor = calculateGCD(x, y);

  // 简化比率
  const simplifiedX = x / commonDivisor;
  const simplifiedY = y / commonDivisor;

  // 返回格式化的字符串
  return `${simplifiedX}:${simplifiedY}`;
}

export async function convertPngToBlackAndWhite(pngBlob: Blob): Promise<Blob> {
  return new Promise((resolve, reject) => {
    // 1. Blob 转 Image 对象
    const imageUrl = URL.createObjectURL(pngBlob);
    const img = new Image();

    img.onload = () => {
      // 释放临时的 Object URL
      URL.revokeObjectURL(imageUrl);

      // 2. 创建 Canvas
      const canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth; // 使用图片的原始尺寸
      canvas.height = img.naturalHeight;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        return reject(new Error("无法获取 Canvas 2D 上下文"));
      }

      // 3. 绘制图像
      ctx.drawImage(img, 0, 0);

      try {
        // 4. 获取像素数据
        // 注意：如果图片源自其他域且未配置 CORS，这里可能会抛出安全错误
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data; // data 是 Uint8ClampedArray [R, G, B, A, R, G, B, A, ...]

        // 5. 处理像素数据
        for (let i = 0; i < data.length; i += 4) {
          const alpha = data[i + 3]; // 获取 Alpha 通道值

          if (alpha === 0) {
            // 完全透明 -> 白色 (并且设为不透明)
            data[i] = 255;     // Red
            data[i + 1] = 255; // Green
            data[i + 2] = 255; // Blue
            data[i + 3] = 255; // Alpha (变为不透明白色)
          } else {
            // 非完全透明 -> 黑色 (并且设为不透明)
            data[i] = 0;       // Red
            data[i + 1] = 0;   // Green
            data[i + 2] = 0;   // Blue
            data[i + 3] = 255; // Alpha (变为不透明黑色)
          }
        }

        // 6. 写回像素数据
        ctx.putImageData(imageData, 0, 0);

        // 7. Canvas 转 Blob
        canvas.toBlob((resultBlob) => {
          if (resultBlob) {
            resolve(resultBlob);
          } else {
            reject(new Error("Canvas 转换为 Blob 失败"));
          }
        }, 'image/png'); // 指定输出格式为 PNG

      } catch (error) {
        reject(new Error(`处理图像数据时出错：${error}`));
      }
    };

    img.onerror = (error) => {
      // 释放临时的 Object URL
      URL.revokeObjectURL(imageUrl);
      reject(new Error(`加载图片失败：${error}`));
    };

    // 触发图片加载
    img.src = imageUrl;
  });
}

export async function imageCompose(id: string, width: number, height: number, bgcolor: string = 'transparent') {
  const node = document.getElementById(id) as HTMLElement;
  const base64 = await domtoimage.toPng(node, {
    quality: 1,
    width,
    height,
    bgcolor,
    style: {
      transform: `scale(1)`,
      width: `${width}px`,
      height: `${height}px`,
    }
  });
  const blob = base64ToBlob(base64.split(",")[1]);
  const blackAndWhiteBlob = await convertPngToBlackAndWhite(blob);

  return {
    base64,
    blob,
    blackAndWhiteBlob,
  };
}

export async function uploadImage(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'image');
  const res = await ky.post(`${API_URL}/image/upload`, {
    body: formData,
  }).json<{
    status_code: number;
    message: string;
    data: {
      url: string;
    }
  }>();
  const { status_code, message, data } = res;

  if (status_code === 1) {
    return data.url;
  } else {
    toast.error(message);
    return null;
  }
}

export const indexedDBStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    return (await get(name)) || null
  },
  setItem: async (name: string, value: string): Promise<void> => {
    await set(name, value)
  },
  removeItem: async (name: string): Promise<void> => {
    await del(name)
  },
}


// 领域模型定义 - 根据实际数据格式重新设计
interface ImageGenerationCommand {
  contentUrls: string[];           // 开头的内容图片链接数组
  prompt: string;                  // 中文描述文本
  styleUrls: string[];             // 风格图片链接数组 --sref
  characterUrls: string[];         // 人脸图片链接数组 --cref
  aspectRatio: {                   // 图像比例 --ar
    width: number;
    height: number;
  };
  styleStrength?: number;          // 样式强度 --s
  modelVersion: string;            // 模型版本 --v
}

// 解析结果类型
interface ParseResult {
  success: boolean;
  data?: ImageGenerationCommand;
  errors: string[];
}

export class ImageCommandParser {
  /**
   * 解析图像生成命令字符串
   * 格式：[内容 URLs] [中文 prompt] --ar [比例] --s [强度] --v [版本] --sref [风格 URLs] --cref [人脸 URLs]
   */
  static parse(commandString: string): ParseResult {
    const errors: string[] = [];
    const result: Partial<ImageGenerationCommand> = {};

    try {
      // 1. 先按 -- 分割，第一部分包含内容 URLs 和 prompt
      const parts = commandString.split(/\s+--/);
      const mainPart = parts[0].trim();

      // 2. 解析主要部分：内容 URLs + prompt
      // 策略：按空格分割，URL 都以 https://开头，中文字符开始就是 prompt
      const tokens = mainPart.split(/\s+/);
      const contentUrls: string[] = [];
      let promptStartIndex = 0;

      // 提取开头的 URLs
      for (let i = 0; i < tokens.length; i++) {
        if (tokens[i].startsWith('https://')) {
          contentUrls.push(tokens[i]);
          promptStartIndex = i + 1;
        } else {
          break;
        }
      }

      result.contentUrls = contentUrls;

      // 提取prompt（从第一个非URL token开始到字符串结束）
      const promptTokens = tokens.slice(promptStartIndex);
      result.prompt = promptTokens.join(' ').trim();

      // 3. 解析各种参数
      result.styleUrls = [];
      result.characterUrls = [];

      for (let i = 1; i < parts.length; i++) {
        const part = parts[i].trim();

        if (part.startsWith('ar ')) {
          // 解析图像比例 --ar 16:9
          const ratioMatch = part.match(/ar\s+(\d+):(\d+)/);
          if (ratioMatch) {
            result.aspectRatio = {
              width: parseInt(ratioMatch[1]),
              height: parseInt(ratioMatch[2])
            };
          }
        } else if (part.startsWith('s ')) {
          // 解析样式强度 --s 750
          const strengthMatch = part.match(/s\s+(\d+)/);
          if (strengthMatch) {
            result.styleStrength = parseInt(strengthMatch[1]);
          }
        } else if (part.startsWith('v ')) {
          // 解析模型版本 --v 6.1
          const versionMatch = part.match(/v\s+([\d.]+)/);
          if (versionMatch) {
            result.modelVersion = versionMatch[1];
          }
        } else if (part.startsWith('sref ')) {
          // 解析风格URLs --sref url1 url2 url3
          const urlsText = part.substring(5).trim();
          const urls = urlsText.split(/\s+/).filter(url => url.startsWith('https://'));
          result.styleUrls = urls;
        } else if (part.startsWith('cref ')) {
          // 解析人脸 URLs --cref url1 url2
          const urlsText = part.substring(5).trim();
          const urls = urlsText.split(/\s+/).filter(url => url.startsWith('https://'));
          result.characterUrls = urls;
        }
      }

      // 4. 设置默认值和验证必填项
      if (!result.aspectRatio) {
        errors.push('未找到图像比例信息 --ar');
        result.aspectRatio = { width: 1, height: 1 };
      }

      if (!result.modelVersion) {
        errors.push('未找到模型版本 --v');
        result.modelVersion = '6.1';
      }

      if (!result.prompt || result.prompt.trim().length === 0) {
        errors.push('未找到 prompt 描述文字');
        result.prompt = '';
      }

      return {
        success: errors.length === 0,
        data: result as ImageGenerationCommand,
        errors
      };

    } catch (error) {
      return {
        success: false,
        errors: [`解析过程中发生错误：${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * 验证解析后的数据
   */
  static validate(command: ImageGenerationCommand): string[] {
    const errors: string[] = [];

    if (!command.prompt || command.prompt.trim().length === 0) {
      errors.push('prompt 不能为空');
    }

    if (command.aspectRatio.width <= 0 || command.aspectRatio.height <= 0) {
      errors.push('图像比例必须大于0');
    }

    if (command.contentUrls.length === 0) {
      errors.push('至少需要一个内容图片URL');
    }

    // 验证URL格式
    const urlPattern = /^https:\/\/.+/;

    const allUrls = [
      ...command.contentUrls,
      ...command.styleUrls,
      ...command.characterUrls
    ];

    allUrls.forEach((url) => {
      if (!urlPattern.test(url)) {
        errors.push(`URL 格式无效：${url}`);
      }
    });

    return errors;
  }

  /**
   * 格式化输出解析结果（便于调试）
   */
  static formatResult(command: ImageGenerationCommand): string {
    return `
=== 解析结果 ===
内容 URLs (${command.contentUrls.length}个):
${command.contentUrls.map(url => `  - ${url}`).join('\n')}

Prompt: ${command.prompt}

图像比例：${command.aspectRatio.width}:${command.aspectRatio.height}
${command.styleStrength ? `样式强度: ${command.styleStrength}` : ''}
模型版本：${command.modelVersion}

${command.styleUrls.length > 0 ? `风格URLs (${command.styleUrls.length}个):
${command.styleUrls.map(url => `  - ${url}`).join('\n')}` : ''}

${command.characterUrls.length > 0 ? `人脸URLs (${command.characterUrls.length}个):
${command.characterUrls.map(url => `  - ${url}`).join('\n')}` : ''}
    `.trim();
  }
}

export const creationScrollToBottom = () => {
  const bottom = document.getElementById('creation-scroll-bottom');

  if (bottom) {
    setTimeout(() => {
      bottom.scrollIntoView(false);
    }, 100);
    const scrollTop = document.getElementById('creation-scroll-container')?.scrollTop;
    localStorage.setItem('creation-scroll-position', scrollTop?.toString() || '0');
  }
}

export const getJobTypeClass = (type: JobType): GenerationType => {
  switch (type) {
    case JobType.VIDEO_EXTEND:
      return 'video';
    case JobType.VIDEO_GENERATION:
      return 'video';
    case JobType.VIDEO_UPSCALE:
      return 'video';
    case JobType.ARTICLE_GENERATION:
      return 'article';
    default:
      return 'image';
  }
}