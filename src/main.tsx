import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ThemeProvider } from "./components/theme-provider";
import Root from "./routes/root.tsx";
import ErrorPage from "./error-page.tsx";
import {
  createBrowserRouter,
  RouterProvider,
} from "react-router"
import { TooltipProvider } from '@radix-ui/react-tooltip';
import Home from './routes/home.tsx';
import CreationDetail from './routes/creation_detail.tsx';
import AdvancedEdit from './routes/advanced_edit.tsx';
import AdvancedEditNew from './routes/advanced_edit_new.tsx';
import Login from './routes/login.tsx';
import './index.css'
import creationDetailLoader from './loader/creation_detail.ts';

const router = createBrowserRouter([
  {
    path: "/",
    element: <Root />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "/creation/:jobid",
        element: <CreationDetail />,
        loader: creationDetailLoader,
      },
      {
        path: "/advanced-edit",
        element: <AdvancedEdit />,
      },
      {
        path: "/advanced-edit/new",
        element: <AdvancedEditNew />,
      },
      {
        path: "/login",
        element: <Login />,
      },
    ],
  },
]);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <TooltipProvider>
        <RouterProvider
          router={router}
        />
      </TooltipProvider>
    </ThemeProvider>
  </StrictMode>,
)
