import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSize } from "ahooks";
import AdvancedMoveLayer from "./advanced-move-layer";
import AdvancedEditUpload from "./advanced-edit-upload";
import { useAdvancedEditStore } from "@/stores/advanced-edit";
import { useAdvancedUploadSuccess } from "@/hooks/use-advanced-upload-success";

// const DEFAULT_TEXT_LAYER = (): TextLayer => ({
//   id: Date.now(),
//   type: 'text',
//   width: "auto",
//   height: "auto",
//   text: '你的文字',
//   x: 0,
//   y: 0,
//   rotate: 0,
//   fontSize: 80,
//   fontColor: '#000000',
// })
export default function AdvancedEditView() {
  const { worktop, foregrounds } = useAdvancedEditStore();
  const [worktopScale, setWorktopScale] = useState(1);
  const worktopSize = useMemo(() => ({
    width: (worktop?.width || 0) * worktopScale,
    height: (worktop?.height || 0) * worktopScale,
  }), [worktop, worktopScale]);
  const containerRef = useRef<HTMLDivElement>(null);
  // const toolsRef = useRef<HTMLDivElement>(null);
  const worktopRef = useRef<HTMLDivElement>(null);
  // const colorPickerRef = useRef<HTMLDivElement>(null);
  const containerSize = useSize(containerRef);
  const hasForeground = useMemo(() => foregrounds.length > 0, [foregrounds]);
  // const currentLayerData = useMemo(() => layers.find(l => l.id === currentLayer?.id) || null, [layers, currentLayer]);
  const handleContainerSizeChange = useCallback(() => {
    if (containerSize && worktop) {
      const containerHeight = (containerSize?.height || 0) - 64;
      const containerAspectRatio = containerSize?.width / containerHeight;
      const worktopAspectRatio = worktop?.width / worktop?.height;

      if (worktopAspectRatio > containerAspectRatio && worktop?.width > containerSize?.width) {
        setWorktopScale(containerSize?.width / worktop?.width);
      } else if (worktopAspectRatio < containerAspectRatio && worktop?.height > containerHeight) {
        setWorktopScale(containerHeight / worktop?.height);
      } else {
        setWorktopScale(1);
      }
    }
  }, [worktop, containerSize]);
  const { onSuccess } = useAdvancedUploadSuccess(handleContainerSizeChange);
  useEffect(() => {
    handleContainerSizeChange();
  }, [containerSize, handleContainerSizeChange]);
  // useClickAway(() => {
  //   console.info('click away');
  //   setCurrentForeground(null);
  // }, [worktopRef])

  return (
    <div className="relative h-full flex-1 min-h-96 max-w-full">
      {worktop && hasForeground ? <>
        <div
          className="h-full flex justify-center items-center"
          ref={containerRef}
        >
          <div
            className="bg-white-box dark:bg-black-box bg-16px shadow-lg relative max-w-full max-h-full overflow-hidden"
            style={{
              width: worktopSize?.width,
              height: worktopSize?.height,
            }}
            ref={worktopRef}
          >
            <div
              className="relative overflow-hidden"
              id="advanced-edit"
              style={{
                aspectRatio: worktop.width / worktop.height,
                width: worktop.width,
                height: worktop.height,
                transform: `scale(${worktopScale})`,
                transformOrigin: 'top left',
              }}>
              {foregrounds.map((foreground, index) => (
                <AdvancedMoveLayer
                  layer={foreground}
                  index={index}
                  key={index}
                  scale={worktopScale}
                />
              ))}
            </div>
          </div>
        </div>
      </> : <AdvancedEditUpload
        className="h-full max-w-lg mx-auto flex justify-center items-center flex-col overflow-hidden gap-4"
        onSuccess={onSuccess}
      />}
    </div>
  )
}