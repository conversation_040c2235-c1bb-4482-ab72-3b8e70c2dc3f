import { cn } from "@/lib/utils";
import { useAdvancedEditStore } from "@/stores/advanced-edit";
import { LayerType, ImageLayer } from "@/stores/edit";
import { useClickAway } from "ahooks";
import { MoveDiagonal, MoveDiagonal2 } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

type MoveLayerProps = {
  layer: LayerType,
  index: number,
  scale: number,
}

// 0: 未开始，1: 右下角，2: 左下角，3: 右上角，4: 左上角
type ResizeDirection = 0 | 1 | 2 | 3 | 4;

const MoveLayer = ({
  layer,
  index,
  scale = 1
}: MoveLayerProps) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const bufferCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const originalImageRef = useRef<HTMLImageElement | null>(null);
  const isDrawing = useRef(false);
  const {
    type,
    id,
  } = layer;
  // Add local state for tracking position, size and rotation during active operations
  const [localLayer, setLocalLayer] = useState<LayerType>(layer);
  const {
    currentForeground,
    setCurrentForeground,
    updateForeground,
    operationType,
    brushSize
  } = useAdvancedEditStore();
  const isCurrent = currentForeground?.id === id;
  const [isEditing, setIsEditing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const [isResizing, setIsResizing] = useState<ResizeDirection>(0);
  const [isDrawingState, setIsDrawingState] = useState(false);
  const isDoing = useMemo(() => isDragging || isRotating || isResizing || isDrawingState, [isDragging, isRotating, isResizing, isDrawingState]);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const lastDrawPos = useRef({ x: null as number | null, y: null as number | null });
  const pendingUpdates = useRef(false);
  const animationFrameId = useRef<number | null>(null);

  // 创建缓冲画布
  useEffect(() => {
    if (!bufferCanvasRef.current) {
      bufferCanvasRef.current = document.createElement('canvas');
    }
    return () => {
      bufferCanvasRef.current = null;
      originalImageRef.current = null;
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, []);

  // Update local state whenever the layer prop changes (but not during operations)
  useEffect(() => {
    if (!isDoing) {
      setLocalLayer(layer);
    }
  }, [layer, isDoing]);

  // 预加载原始图像，避免每次绘制时都要加载
  useEffect(() => {
    if (localLayer.type === 'image') {
      const imageLayer = localLayer as ImageLayer;
      if (imageLayer.base64 || imageLayer.rmbgBase64) {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          originalImageRef.current = img;
        };
        img.src = imageLayer.base64 || imageLayer.rmbgBase64 || '';
      }
    }
  }, [localLayer]);

  // Initialize canvas with image when operationType changes to erase or reverseErase
  // 如果 operationType 从 move 变为 erase 或 reverseErase，则初始化画布，否则不操作
  useEffect(() => {
    if ((operationType === 'erase' || operationType === 'reverseErase') &&
      localLayer.type === 'image' &&
      canvasRef.current &&
      isCurrent) {
      console.info('初始化画布');
      const canvas = canvasRef.current;
      const buffer = bufferCanvasRef.current;

      if (!buffer) return;

      // 设置画布尺寸
      const width = Number(localLayer.width);
      const height = Number(localLayer.height);
      canvas.width = width;
      canvas.height = height;
      buffer.width = width;
      buffer.height = height;

      // 提前准备好缓冲画布
      const bufferCtx = buffer.getContext('2d', { willReadFrequently: true });
      const canvasCtx = canvas.getContext('2d', { willReadFrequently: true });

      if (!bufferCtx || !canvasCtx) return;

      // 准备要绘制的图像
      const drawImage = () => {
        // if (originalImageRef.current) {
        //   // 如果已有预加载图像，直接使用
        //   bufferCtx.clearRect(0, 0, width, height);
        //   bufferCtx.drawImage(originalImageRef.current, 0, 0, width, height);
        //   canvasCtx.clearRect(0, 0, width, height);
        //   canvasCtx.drawImage(originalImageRef.current, 0, 0, width, height);
        //   return;
        // }

        // 否则加载图像
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
          originalImageRef.current = img;
          bufferCtx.clearRect(0, 0, width, height);
          bufferCtx.drawImage(img, 0, 0, width, height);
          canvasCtx.clearRect(0, 0, width, height);
          canvasCtx.drawImage(img, 0, 0, width, height);
        };

        if (localLayer.rmbgBase64) {
          img.src = localLayer.rmbgBase64;
        } else if (localLayer.base64) {
          img.src = localLayer.base64;
        } else if (localLayer.url) {
          img.src = localLayer.url;
        }
      };

      drawImage();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [operationType, isCurrent]);

  // 延迟更新 base64 数据的函数 - 只在绘制结束时调用一次
  const applyCanvasChanges = useCallback(() => {
    if (pendingUpdates.current && canvasRef.current && localLayer.type === 'image') {
      try {
        const base64 = canvasRef.current.toDataURL('image/png');
        setLocalLayer(prev => ({
          ...prev,
          rmbgBase64: base64
        }));
        updateForeground(id, {
          ...localLayer,
          rmbgBase64: base64
        });
        pendingUpdates.current = false;
      } catch (error) {
        console.error('Error exporting canvas:', error);
      }
    }
  }, [localLayer, id, updateForeground]);

  // Draw on canvas
  const handleDraw = useCallback((x: number, y: number) => {
    if (!canvasRef.current || !bufferCanvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    const bufferCanvas = bufferCanvasRef.current;
    const bufferCtx = bufferCanvas.getContext('2d', { willReadFrequently: true });

    if (!ctx || !bufferCtx) return;

    // 设置刷子属性
    ctx.lineWidth = brushSize * 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    bufferCtx.lineWidth = brushSize * 2;
    bufferCtx.lineCap = 'round';
    bufferCtx.lineJoin = 'round';

    if (operationType === 'erase') {
      // 擦除操作
      ctx.globalCompositeOperation = 'destination-out';
      bufferCtx.globalCompositeOperation = 'destination-out';

      if (lastDrawPos.current.x === null || lastDrawPos.current.y === null) {
        // 第一个点
        ctx.beginPath();
        ctx.arc(x, y, brushSize, 0, Math.PI * 2);
        ctx.fill();

        bufferCtx.beginPath();
        bufferCtx.arc(x, y, brushSize, 0, Math.PI * 2);
        bufferCtx.fill();
      } else {
        // 连接点
        ctx.beginPath();
        ctx.moveTo(lastDrawPos.current.x, lastDrawPos.current.y);
        ctx.lineTo(x, y);
        ctx.stroke();

        bufferCtx.beginPath();
        bufferCtx.moveTo(lastDrawPos.current.x, lastDrawPos.current.y);
        bufferCtx.lineTo(x, y);
        bufferCtx.stroke();
      }
    } else if (operationType === 'reverseErase') {
      // 还原操作
      // 需要从原始图像中恢复
      if (!originalImageRef.current) return;

      // 直接在当前画布上应用圆形印章
      stampOriginalImage(ctx, originalImageRef.current, x, y, brushSize, canvas);
      stampOriginalImage(bufferCtx, originalImageRef.current, x, y, brushSize, bufferCanvas);

      // 如果距离足够远，填充中间点
      if (lastDrawPos.current.x !== null && lastDrawPos.current.y !== null) {
        const dx = x - lastDrawPos.current.x;
        const dy = y - lastDrawPos.current.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > brushSize / 2) {
          const numPoints = Math.ceil(distance / (brushSize / 3));

          for (let i = 1; i < numPoints; i++) {
            const t = i / numPoints;
            const stampX = lastDrawPos.current.x + dx * t;
            const stampY = lastDrawPos.current.y + dy * t;

            stampOriginalImage(ctx, originalImageRef.current, stampX, stampY, brushSize, canvas);
            stampOriginalImage(bufferCtx, originalImageRef.current, stampX, stampY, brushSize, bufferCanvas);
          }
        }
      }
    }

    lastDrawPos.current = { x, y };
    pendingUpdates.current = true;
  }, [brushSize, operationType]);

  // 辅助函数：在指定位置应用原始图像的圆形印章
  const stampOriginalImage = (ctx: CanvasRenderingContext2D, img: HTMLImageElement, x: number, y: number, size: number, canvas: HTMLCanvasElement) => {
    ctx.save();

    // 创建圆形剪切区域
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.clip();

    // 在剪切区域绘制原始图像
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    ctx.restore();
  };

  const handleResizeMouseDown = (e: React.MouseEvent, direction: ResizeDirection) => {
    if (operationType !== 'move') return;
    setIsResizing(direction);
    lastMousePos.current = { x: e.clientX, y: e.clientY };
  }

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isEditing && type === 'text') return;

    if (operationType === 'move') {
      setIsDragging(true);
      lastMousePos.current = { x: e.clientX, y: e.clientY };
    } else if ((operationType === 'erase' || operationType === 'reverseErase') &&
      localLayer.type === 'image' &&
      canvasRef.current) {
      setIsDrawingState(true);
      isDrawing.current = true;
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;
      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      // 重置绘制位置
      lastDrawPos.current = { x: null, y: null };
      lastMousePos.current = { x, y };
      handleDraw(x, y);
    }
  };

  // 处理鼠标移动事件
  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && operationType === 'move') {
      const deltaX = e.clientX - lastMousePos.current.x;
      const deltaY = e.clientY - lastMousePos.current.y;

      setLocalLayer(prev => ({
        ...prev,
        x: prev.x + deltaX,
        y: prev.y + deltaY,
      }));

      lastMousePos.current = { x: e.clientX, y: e.clientY };
    }

    if (isRotating && operationType === 'move') {
      // 计算矩形中心点
      const rect = document.querySelector('.current-active')?.getBoundingClientRect();
      if (!rect) return;

      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // 计算鼠标相对于中心点的角度
      const mouseAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX);
      const prevAngle = Math.atan2(lastMousePos.current.y - centerY, lastMousePos.current.x - centerX);

      // 计算角度差值并转换为度数
      const deltaAngle = (mouseAngle - prevAngle) * (180 / Math.PI);

      // 更新旋转角度，累加差值
      const newRotate = ((localLayer.rotate || 0) + deltaAngle) % 360;

      setLocalLayer(prev => ({
        ...prev,
        rotate: newRotate,
      }));

      lastMousePos.current = { x: e.clientX, y: e.clientY };
    }

    // 根据不同触发点调整图片大小位置及大小
    if (isResizing && localLayer.type === 'image' && operationType === 'move') {
      const deltaX = e.clientX - lastMousePos.current.x;
      const deltaY = e.clientY - lastMousePos.current.y;
      const imageLayer = localLayer;
      let newWidth, newHeight, newX = imageLayer.x, newY = imageLayer.y;

      // 计算原始宽高比
      const { aspectRatio } = localLayer;

      // 根据不同的调整点处理大小和位置
      switch (isResizing) {
        case 1: // 右下角 - 只调整大小
          // 使用较大的变化来决定调整方向，但保持宽高比
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = Number(imageLayer.width) + deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = Number(imageLayer.height) + deltaY;
            newWidth = newHeight * aspectRatio;
          }
          break;
        case 2: // 左下角 - 调整大小和 X 位置
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = Number(imageLayer.width) - deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = Number(imageLayer.height) + deltaY;
            newWidth = newHeight * aspectRatio;
          }
          newX = imageLayer.x + (Number(imageLayer.width) - newWidth);
          break;
        case 3: // 右上角 - 调整大小和 Y 位置
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = Number(imageLayer.width) + deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = Number(imageLayer.height) - deltaY;
            newWidth = newHeight * aspectRatio;
          }
          newY = imageLayer.y + (Number(imageLayer.height) - newHeight);
          break;
        case 4: // 左上角 - 调整大小和 XY 位置
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = Number(imageLayer.width) - deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = Number(imageLayer.height) - deltaY;
            newWidth = newHeight * aspectRatio;
          }
          newX = imageLayer.x + (Number(imageLayer.width) - newWidth);
          newY = imageLayer.y + (Number(imageLayer.height) - newHeight);
          break;
      }

      // 确保尺寸不会太小
      if (newWidth < 20 || newHeight < 20) {
        lastMousePos.current = { x: e.clientX, y: e.clientY };
        return;
      }

      setLocalLayer(prev => ({
        ...prev,
        width: newWidth,
        height: newHeight,
        x: newX,
        y: newY
      }));

      lastMousePos.current = { x: e.clientX, y: e.clientY };
    }

    // 处理绘制
    if (isDrawing.current && (operationType === 'erase' || operationType === 'reverseErase') &&
      canvasRef.current && localLayer.type === 'image') {

      // 使用 requestAnimationFrame 优化绘制性能
      if (animationFrameId.current === null) {
        animationFrameId.current = requestAnimationFrame(() => {
          if (canvasRef.current) {
            const canvas = canvasRef.current;
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const x = (e.clientX - rect.left) * scaleX;
            const y = (e.clientY - rect.top) * scaleY;

            handleDraw(x, y);

            // 重置动画帧 ID
            animationFrameId.current = null;
          }
        });
      }
    }
  }, [isDragging, operationType, isRotating, isResizing, localLayer, handleDraw]);

  // 处理鼠标松开事件
  const handleGlobalMouseUp = useCallback(() => {
    // 更新 store
    if (isDragging || isRotating || isResizing) {
      try {
        updateForeground(id, localLayer);
      } catch (error) {
        console.error('Failed to update foreground:', error);
      }
    }

    // 如果是绘制操作，应用 canvas 的更改
    if (isDrawing.current) {
      applyCanvasChanges();
    }

    // 重置所有状态
    setIsDragging(false);
    setIsRotating(false);
    setIsResizing(0);
    setIsDrawingState(false);
    isDrawing.current = false;
    lastDrawPos.current = { x: null, y: null };

    // 取消任何待处理的动画帧
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }
  }, [id, isDragging, isResizing, isRotating, localLayer, updateForeground, applyCanvasChanges]);

  const handleEditText = useCallback(() => {
    if (type !== 'text') return;
    setIsEditing(true);
  }, [type]);

  // 在组件挂载时添加全局事件监听
  useEffect(() => {
    document.body.addEventListener('mouseup', handleGlobalMouseUp);
    document.body.addEventListener('mousemove', handleGlobalMouseMove);
    document.body.addEventListener('mouseleave', handleGlobalMouseUp);
    // 在组件卸载时移除事件监听
    return () => {
      document.body.removeEventListener('mouseup', handleGlobalMouseUp);
      document.body.removeEventListener('mousemove', handleGlobalMouseMove);
      document.body.removeEventListener('mouseleave', handleGlobalMouseUp);
    };
  }, [handleGlobalMouseUp, handleGlobalMouseMove]);

  useClickAway(() => {
    setIsEditing(false);
  }, [textRef])

  // Determine cursor type based on operationType
  const getCursorStyle = () => {
    if (type !== 'image') return 'cursor-grab active:cursor-grabbing';

    switch (operationType) {
      case 'move':
        return 'cursor-grab active:cursor-grabbing';
      case 'erase':
        return 'cursor-eraser';
      case 'reverseErase':
        return 'cursor-cell';
      default:
        return 'cursor-grab active:cursor-grabbing';
    }
  };

  return <div
    className={cn("absolute group top-0 left-0", getCursorStyle(), {
      "current-active": isCurrent,
      // "z-[999]": isCurrent,
      "pointer-events-none user-select-none": !isCurrent && operationType !== 'move',
    })}
    style={{
      width: localLayer.width,
      height: localLayer.height,
      transform: `translate(${localLayer.x / scale}px, ${localLayer.y / scale}px) rotate(${localLayer.rotate}deg)`,
    }}
    onMouseDown={() => operationType === 'move' && setCurrentForeground(layer)}
  >
    {isCurrent && <>
      <div className="absolute z-[999] top-0 left-0 right-0 h-[3px] bg-blue-500" style={{
        transform: `scaleY(calc(1 / ${scale}))`
      }} />
      <div className="absolute z-[999] top-0 right-0 bottom-0 w-[3px] bg-blue-500" style={{
        transform: `scaleX(calc(1 / ${scale}))`
      }} />
      <div className="absolute z-[999] bottom-0 left-0 right-0 h-[3px] bg-blue-500" style={{
        transform: `scaleY(calc(1 / ${scale}))`
      }} />
      <div className="absolute z-[999] bottom-0 left-0 top-0 w-[3px] bg-blue-500" style={{
        transform: `scaleX(calc(1 / ${scale}))`
      }} />

      {localLayer.type === 'image' && operationType === 'move' && <>
        {/* 右下角 */}
        <div
          className="absolute z-[999] select-none bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-6 h-6 bg-muted border hover:bg-blue-500 hover:text-white cursor-nwse-resize hidden group-[.current-active]:flex rounded items-center justify-center"
          title="改变大小"
          style={{
            transform: `scale(calc(1 / ${scale}))`
          }}
          onMouseDown={(e) => handleResizeMouseDown(e, 1)}
        >
          <MoveDiagonal2 className="w-4 h-4" />
        </div>
        {/* 左下角 */}
        <div
          className="absolute z-[999] select-none bottom-0 left-0 -translate-x-1/2 translate-y-1/2 w-6 h-6 bg-muted border hover:bg-blue-500 hover:text-white cursor-nesw-resize hidden group-[.current-active]:flex rounded items-center justify-center"
          title="改变大小"
          style={{
            transform: `scale(calc(1 / ${scale}))`
          }}
          onMouseDown={(e) => handleResizeMouseDown(e, 2)}
        >
          <MoveDiagonal className="w-4 h-4" />
        </div>
        {/* 右上角 */}
        <div
          className="absolute z-[999] select-none top-0 right-0 translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-muted border hover:bg-blue-500 hover:text-white cursor-nesw-resize hidden group-[.current-active]:flex rounded items-center justify-center"
          title="改变大小"
          style={{
            transform: `scale(calc(1 / ${scale}))`
          }}
          onMouseDown={(e) => handleResizeMouseDown(e, 3)}
        >
          <MoveDiagonal className="w-4 h-4" />
        </div>
        {/* 左上角 */}
        <div
          className="absolute z-[999] select-none top-0 left-0 -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-muted border hover:bg-blue-500 hover:text-white cursor-nwse-resize hidden group-[.current-active]:flex rounded items-center justify-center"
          title="改变大小"
          style={{
            transform: `scale(calc(1 / ${scale}))`
          }}
          onMouseDown={(e) => handleResizeMouseDown(e, 4)}
        >
          <MoveDiagonal2 className="w-4 h-4" />
        </div>
      </>}
    </>}
    {
      localLayer.type === 'image' ? (
        <>
          <img
            src={localLayer.rmbgBase64 || localLayer.url || localLayer.base64}
            alt={id.toString()}
            className="w-full h-full object-cover"
            style={{
              zIndex: index,
              display: (operationType === 'erase' || operationType === 'reverseErase') && isCurrent ? 'none' : 'block',
            }}
            onMouseDown={handleMouseDown}
            draggable="false"
            onDragStart={(e) => e.preventDefault()}
          />
          {(operationType === 'erase' || operationType === 'reverseErase') && isCurrent && (
            <canvas
              ref={canvasRef}
              className="w-full h-full"
              style={{ zIndex: index }}
              onMouseDown={handleMouseDown}
            />
          )}
        </>
      ) : (
        <p
          className={cn("py-2 px-4 select-none overflow-hidden", {
            "cursor-text select-auto": isEditing,
          })}
          title="双击进行编辑"
          ref={textRef}
          onMouseDown={handleMouseDown}
          onDoubleClick={handleEditText}
          style={{
            zIndex: index,
            fontSize: `${(localLayer).fontSize}px`,
            fontFamily: localLayer.fontFamily,
            fontWeight: localLayer.fontWeight,
            fontStyle: localLayer.fontStyle,
            color: localLayer.fontColor,
            direction: localLayer.direction,
            writingMode: localLayer.writingMode,
            textDecoration: `${localLayer.textDecorationLine} ${localLayer.textDecorationStyle}`,
          }}
          contentEditable={isEditing}
          draggable="false"
          onDragStart={(e) => e.preventDefault()}
        >
          {localLayer.text}
        </p>
      )}
  </div>
}

export default MoveLayer;
