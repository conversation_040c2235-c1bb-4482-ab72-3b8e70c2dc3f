import { cn, getJobTypeClass, ImageCommandParser } from "@/lib/utils";
import { DiffusionStatus, GenerationType, JobItemType, JobType, motionType, useCreationStore } from "@/stores/creation";
import { useHover, useUnmount } from "ahooks";
import { useInterval } from "ahooks";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { Badge } from "./ui/badge";
import { Bean, Clock10, Play, RotateCcw, Type, Video } from "lucide-react";
import { Button } from "./ui/button";
import { NavLink } from "react-router";
import Basemap from "./basemap";
import CreationPrompt from "./creation-prompt";
import { toast } from "sonner";
import CopyrightTag from "./copyright-tag";

type CreationBoxProps = {
  job: JobItemType;
}
export default function CreationBox({
  job,
}: CreationBoxProps) {
  const refVideoBoxs = useRef<HTMLDivElement>(null);
  const {
    job_id,
    text,
    created_at,
    urls = [],
    comment = '未知原因',
    status,
    type = JobType.DEFAULT,
    is_copyright = 0,
    seed,
    basemap = []
  } = job;
  const {
    getJobInfo,
    updateJob,
    setPrompt,
    jobReroll,
    setBasemap,
    variation,
    setGenerationType,
    autoExtendVideo,
    setVideoExtend,
    setVideoFirstFrame,
    generationType: generationTypeStore,
  } = useCreationStore();

  const loading = useMemo(() => !(status === DiffusionStatus.COMPLETED || status === DiffusionStatus.FAILED), [status]);
  // const { success, data, errors } = useMemo(() => ImageCommandParser.parse(text), [text]);
  const { data } = useMemo(() => ImageCommandParser.parse(text), [text]);
  const { width: w, height: h } = useMemo(() => data?.aspectRatio || { width: 1, height: 1 }, [data]);
  const prompt = useMemo(() => data?.prompt || text, [data, text]);
  const generationType: GenerationType = useMemo(() => {
    return getJobTypeClass(type);
  }, [type]);

  const handleGetJobInfo = useCallback(async () => {
    const { status_code, data, message } = await getJobInfo(job_id);

    if (status_code === 1) {
      if (data.status === DiffusionStatus.COMPLETED) {
        updateJob(job_id, {
          status: data.status,
          urls: data.urls,
          seed: data.seed,
          updatedAt: new Date().toISOString(),
        });
      }

      if (data.status === DiffusionStatus.FAILED) {
        updateJob(job_id, {
          status: data.status,
          comment: data.comment,
          updatedAt: new Date().toISOString(),
        });
        toast.error(data.comment || `生成失败`);
      }
    } else if (status_code === 0) {
      updateJob(job_id, {
        status: DiffusionStatus.FAILED,
        comment: message,
        updatedAt: new Date().toISOString(),
      });
      toast.error(data.comment || `生成失败`);
    }
  }, [getJobInfo, job_id, updateJob]);
  const handleGetScrollPosition = useCallback(() => {
    const scrollTop = document.getElementById('creation-scroll-container')?.scrollTop;
    localStorage.setItem('creation-scroll-position', scrollTop?.toString() || '0');
  }, []);
  const handleVideoExtend = useCallback((jobId: string, index: number, url: string, motion: motionType) => {
    if (generationTypeStore !== 'video') {
      setGenerationType('video');
    }
    setVideoExtend({
      type: 'video_extend',
      jobId,
      videoNo: index,
      motion,
      url,
      is_copyright
    })
    setPrompt(prompt + ' --motion ' + motion);
    toast.success('请点击“生成视频”按钮进行视频延长')
  }, [generationTypeStore, setVideoExtend, is_copyright, setPrompt, prompt, setGenerationType])
  const clearTimer = useInterval(() => {
    handleGetJobInfo()
  }, status === DiffusionStatus.COMPLETED || status === DiffusionStatus.FAILED ? undefined : 2000)
  const isHovering = useHover(refVideoBoxs);

  useUnmount(() => {
    clearTimer()
  })

  useEffect(() => {
    const videos = refVideoBoxs.current?.querySelectorAll('video');

    if (videos) {
      videos.forEach(async (video) => {
        try {
          if (isHovering) {
            await video.play();
          } else {
            video.pause();
          }
        } catch (error) {
          // 忽略播放被中断的错误，这是正常的用户交互行为
          if (error instanceof Error && error.name !== 'AbortError') {
            console.warn('视频播放错误：', error);
          }
        }
      });
    }
  }, [isHovering]);

  return (
    <div className={cn("flex flex-col xl:flex-row gap-2 p-1 rounded-md bg-muted mb-2 xl:mb-0 xl:bg-transparent xl:hover:bg-muted", {
      "animate-pulse": loading
    })}>
      {status === DiffusionStatus.FAILED ? (
        <div className="flex-1 flex flex-col gap-2 justify-center items-center bg-muted/70 rounded-md py-12 px-4">
          <p className="text-sm text-muted-foreground text-center">生成失败</p>
          <p className="text-xs text-muted-foreground text-center">{comment}</p>
        </div>
      ) : (
        <div
          className={cn("flex-1 gap-1 xl:gap-2 grid", {
            'grid-cols-2': w > h,
            'grid-cols-4': w <= h,
          })}
          ref={refVideoBoxs}
        >
          {loading ? [...Array((type === JobType.REMOVE_BACKGROUND || type === JobType.VIDEO_UPSCALE || type === JobType.UPSCALE) ? 1 : 4)].map((_, index) => (
            <div
              key={index}
              className={cn("w-full h-full bg-zinc-200 dark:bg-zinc-800 animate-pulse flex justify-center items-center", {
                "rounded-l-md": index === 0 || (w > h && index % 2 === 0),
                "rounded-r-md": (w > h && index % 2 === 1) || index === 3,
              })}
              style={{
                aspectRatio: `${w / h}`,
              }}
            >
              <p className="text-muted-foreground text-sm select-none">生成中...</p>
            </div>
          )) : <>
            {urls?.map((url, index) => (
              <div
                className={cn("relative bg-[length:16px_16px] bg-white-box dark:bg-black-box overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 group", {
                  "rounded-l-md": index === 0 || (w > h && index % 2 === 0),
                  "rounded-r-md": index === urls.length - 1 || (w > h && index % 2 === 1),
                })}
                key={url + index}
                style={{
                  aspectRatio: `${w / h}`,
                }}
              >
                {type !== JobType.REMOVE_BACKGROUND && <CopyrightTag is_copyright={is_copyright} className="absolute top-1 right-1 z-10" align="end" />}
                <NavLink
                  key={url}
                  to={`/creation/${job_id}?index=${index}`}
                  className="absolute top-0 left-0 size-full"
                  viewTransition
                  prefetch="viewport"
                  onClick={handleGetScrollPosition}
                >
                  {({ isTransitioning }) => (
                    <>
                      {generationType === 'image' && <img
                        src={url + `?x-oss-process=image/format,webp`}
                        key={url}
                        alt={`${job_id}-${index}`}
                        loading="lazy"
                        className="contain-layout w-full h-full object-cover"
                        style={{
                          viewTransitionName: isTransitioning ? `creation-image-${index}` : '',
                        }}
                      />}
                      {generationType === 'video' && <>
                        <Play className="size-4 absolute top-3 left-3 z-10 text-white opacity-90 group-hover:opacity-0 transition-opacity duration-300" />
                        <video
                          src={url}
                          key={url}
                          muted
                          loop
                          preload="metadata"
                          className="contain-layout w-full h-full object-cover"
                          style={{
                            viewTransitionName: isTransitioning ? `creation-video-${index}` : '',
                          }}
                        />
                      </>}
                    </>
                  )}
                </NavLink>
                <div className="absolute bottom-0 px-1 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex flex-wrap justify-center items-center gap-1 w-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {generationType === 'image' && (<>
                    {type !== JobType.REMOVE_BACKGROUND && <div className="flex gap-1">
                      <Button variant="outline" title="变化" size="sm" className="font-normal gap-1" onClick={() => variation(job_id, index, 0)}>
                        <span className="text-xs">V</span>细微
                      </Button>
                      <Button variant="outline" title="变化" size="sm" className="font-normal gap-1" onClick={() => variation(job_id, index, 1)}>
                        <span className="text-xs">V</span>强烈
                      </Button>
                    </div>}
                    <Button
                      variant="outline"
                      title="生成视频"
                      size="sm"
                      className="font-normal gap-1"
                      onClick={() => setVideoFirstFrame({
                        type: 'video_first_frame',
                        url,
                        width: w,
                        height: h,
                        is_copyright,
                        jobId: job_id,
                        imageNo: index,
                      })}>
                      <Video className="size-3" />视频首帧
                    </Button>
                  </>)}
                  {generationType === 'video' && type !== JobType.VIDEO_UPSCALE && <>
                    <Button
                      variant="outline"
                      title="视频延长"
                      size="sm"
                      className="font-normal gap-1"
                      onClick={() => autoExtendVideo(job_id, index, 'low')}
                    >
                      <span className="text-xs">E</span>自动
                    </Button>
                    <Button
                      variant="outline"
                      title="视频延长"
                      size="sm"
                      className="font-normal gap-1"
                      onClick={() => handleVideoExtend(job_id, index, url, 'low')}
                    >
                      <span className="text-xs">E</span>手动
                    </Button>
                  </>}
                </div>
              </div>
            ))}
          </>}
        </div>)}
      <div className="w-full xl:w-96 flex flex-col justify-start items-start gap-1 xl:gap-2 relative group">
        <div
          className="sticky top-4 z-10 flex flex-col gap-1 w-full"
          style={{
            viewTransitionName: `creation-image-info-${job_id}`,
          }}
        >
          <CreationPrompt prompt={prompt} setPrompt={setPrompt} type={type} />
          {created_at && <Badge variant="outline" className="text-muted-foreground font-light px-1"><Clock10 className="size-3 mr-1" />{created_at}</Badge>}
          <Basemap className="-ml-2" basemap={basemap} boxType={generationType} />
        </div>
        <div className="flex gap-1 mt-auto w-full pt-1 xl:opacity-0 xl:group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex flex-wrap gap-1 flex-1">
            {type !== JobType.REMOVE_BACKGROUND && status !== DiffusionStatus.FAILED && <Button variant="outline" size="sm" disabled={loading} className="font-normal" onClick={() => {
              jobReroll(job_id)
            }}>
              <RotateCcw className="size-3" />重新生成
            </Button>}
            <Button variant="outline" size="sm" className="font-normal" onClick={() => {
              setPrompt(prompt);
              setBasemap(basemap);
              setGenerationType(generationType);
            }}>
              <Type className="size-3" />使用整体
            </Button>
          </div>
          {!!seed && seed !== '0' && <Button
            variant="outline"
            size="sm"
            title="点击复制种子"
            className="font-normal"
            onClick={() => {
              navigator.clipboard.writeText(seed.toString());
              toast.success('复制成功');
            }}
          >
            <Bean className="size-3" />{seed}
          </Button>}
        </div>
      </div>
    </div>
  )
}