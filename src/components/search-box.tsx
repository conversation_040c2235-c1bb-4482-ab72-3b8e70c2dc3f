import { useCreationStore, VCGImageType } from "@/stores/creation";
import { useCallback, useMemo } from "react";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import { Blend, Image, Smile, Film } from "lucide-react";

type SearchBoxProps = {
  result: VCGImageType;
}
export default function SearchBox({
  result,
}: SearchBoxProps) {
  const {
    title,
    url,
    id,
    width,
    height
  } = result;
  const {
    addBasemap,
    basemap,
    setVideoFirstFrame,
  } = useCreationStore();
  const handleSetVideoParameter = useCallback(() => {
    setVideoFirstFrame({
      type: 'video_first_frame',
      url,
      width,
      height,
      vcgId: id,
      is_copyright: 1,
    });
  }, [setVideoFirstFrame, id, url, width, height]);
  const isSelected = useMemo(() => {
    return basemap.some((item) => item.url === url);
  }, [basemap, url]);

  return (
    <div
      className={cn("relative rounded overflow-hidden mb-2 group")}
    >
      {isSelected && (
        <div className="absolute top-3 right-3 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
      <img
        src={url + `?x-oss-process=image/format,webp`}
        key={url}
        alt={title}
        loading="lazy"
        className="w-full h-full object-cover"
      />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex gap-1 items-center flex-wrap w-full justify-center p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Button
          variant="outline"
          size="sm"
          onClick={() => addBasemap([{
            url,
            type: 'content',
            width,
            height,
            vcgId: id,
            is_copyright: 1,
          }])}
        >
          <Image className="size-4" />参照底图
        </Button>
        <Button variant="outline" size="sm" onClick={() => addBasemap([{
          url,
          type: 'sref',
          width,
          height,
          vcgId: id,
          is_copyright: 1,
        }])}>
          <Blend className="size-4" />参照风格
        </Button>
        <Button variant="outline" size="sm" onClick={() => addBasemap([{
          url,
          type: 'cref',
          width,
          height,
          vcgId: id,
          is_copyright: 1,
        }])}>
          <Smile className="size-4" />参照角色
        </Button>
        <Button variant="outline" size="sm" onClick={handleSetVideoParameter}>
          <Film className="size-4" />设置视频首帧
        </Button>
      </div>
      <div
        className="absolute bottom-0 left-0 right-0 p-2 py-1.5 bg-black/10 backdrop-blur text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        title={title}
      >
        <p className="text-sm truncate">{title}</p>
      </div>
    </div>
  )
}