import { JobItemType } from "@/stores/creation"
import CreationEditBody from "./creation-edit-body";
import { useNavigate } from "react-router";
import { useKeyPress } from "ahooks";
interface CreationEditProps {
  job?: JobItemType,
  index: number
}

export default function CreationEdit({ job, index }: CreationEditProps) {
  const navigate = useNavigate();

  useKeyPress(
    (event) => ['Escape', 'ArrowUp', 'ArrowDown'].includes(event.key),
    (event) => {
      if (event.key === 'Escape') {
        navigate(-1)
      } else if (event.key === 'ArrowUp') {
        // setEstablishingShot(establishingShot.map(({ id, type }) => ({ id, type: type === 'content' ? 'sref' : 'content' })));
      } else if (event.key === 'ArrowDown') {
        // setEstablishingShot(establishingShot.map(({ id, type }) => ({ id, type: type === 'content' ? 'cref' : 'content' })));
      }
    },
    {
      target: document.body,
    }
  )

  if (!job) {
    return <div className="relative h-full flex-1 min-h-96 overflow-hidden flex">
      <div className="flex-1 relative bg-muted h-full p-4 pb-28 overflow-hidden flex justify-center items-center">
        <div className="text-sm text-muted-foreground">未找到对应的图片</div>
      </div>
    </div>
  }

  return (
    <CreationEditBody job={job} index={index} />
  )
}