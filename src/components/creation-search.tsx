import { useMemo, useRef, useEffect } from "react";
import Masonry from "react-masonry-css";
import { useCreationStore } from "@/stores/creation";
import { Loader, Search } from "lucide-react";
import { useUpdateLayoutEffect } from "ahooks";
import SearchInput from "./search-input";
import SearchBox from "./search-box";
import { cn } from "@/lib/utils";

export default function CreationSearch() {
  // const location = useLocation();
  const {
    searchQuery,
    searchLoading,
    searchResults,
  } = useCreationStore();
  const hasResults = useMemo(() => searchResults.length > 0, [searchResults]);
  const resultsLength = useMemo(() => searchResults.length, [searchResults]);
  const lastRef = useRef<HTMLDivElement>(null);
  const scrollToBottom = () => {
    lastRef.current?.scrollIntoView(false);
  }
  // 恢复滚动位置
  useEffect(() => {
    const scrollPosition = localStorage.getItem('creation-scroll-position');

    if (scrollPosition) {
      document.getElementById('creation-scroll-container')?.scrollTo(0, parseInt(scrollPosition || '0'));
    }
  }, []);
  useUpdateLayoutEffect(() => {
    if (resultsLength > 0) {
      scrollToBottom();
    }
  }, [resultsLength]);

  const breakpointColumnsObj = {
    default: 6,
    1536: 5,
    1280: 4,
    768: 2
  };

  return (
    <div className="relative h-full bg-background">
      {hasResults ? (<div className={cn("w-full h-full overflow-y-auto relative p-4", {
        "overflow-hidden": searchLoading,
      })}>
        <div
          className={cn("absolute top-0 left-0 opacity-0 pointer-events-none right-0 bottom-0 p-4 bg-gradient-to-b from-background to-transparent z-50 backdrop-blur-sm transition-opacity duration-300", {
            "opacity-100 pointer-events-auto": searchLoading,
          })}
        >
          <p className="text-muted-foreground text-center pt-12 mb-2">
            <Loader className="inline-block mr-2 vertical-middle size-6 -mt-1 animate-spin" />
            正在搜索：<span className="text-center">{searchQuery}</span>
          </p>
        </div>
        {/* <div className="text-sm bg-blue-500 rounded px-2 py-1 text-white mb-3 inline-block">
          智能搜索
        </div> */}
        {/* <h3 className="text-sm mb-2">"{searchQuery}" 的搜索结果</h3> */}
        <div className="container mx-auto pb-36">
          <div ref={lastRef} className="h-[1px] w-full opacity-0" />
          <Masonry
            breakpointCols={breakpointColumnsObj}
            className="my-masonry-grid"
            columnClassName="my-masonry-grid_column"
          >
            {searchResults.map((item) => (
              <SearchBox key={item.id} result={item} />
            ))}
          </Masonry>
          <p className="text-xs text-muted-foreground text-center pt-4 opacity-50">到底了...</p>
        </div>
      </div>) : (<div className="h-full flex flex-col justify-center items-center gap-3 px-4 text-center">
        <h2 className="text-xl font-bold">{searchLoading ? <><Loader className="inline-block mr-2 vertical-middle size-6 -mt-1 animate-spin" />版权素材搜索中...</> : <><Search className="inline-block mr-2 vertical-middle size-6 -mt-1" />搜索版权参考素材</>}</h2>
        <p className="text-sm text-muted-foreground">请在下方输入您需要的图片描述，按回车键开始搜索图片素材</p>
      </div>)}
      <SearchInput className="w-full absolute bottom-4 px-4 left-1/2 -translate-x-1/2 z-10" onSearch={scrollToBottom} />
    </div>
  )
}