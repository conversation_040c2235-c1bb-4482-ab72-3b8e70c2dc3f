import { useAdvancedUploadSuccess } from "@/hooks/use-advanced-upload-success";
import AdvancedEditUpload from "./advanced-edit-upload";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { useState } from "react";

interface AdvancedUploadDialogProps {
  children: React.ReactNode;
}

export default function AdvancedUploadDialog({ children }: AdvancedUploadDialogProps) {
  const [open, setOpen] = useState(false);
  const { onSuccess } = useAdvancedUploadSuccess(() => {
    setOpen(false); // 上传成功后关闭 Dialog
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>上传图片</DialogTitle>
          <DialogDescription>
            上传图片，开始编辑
          </DialogDescription>
        </DialogHeader>
        <AdvancedEditUpload className="w-full" onSuccess={onSuccess} />
      </DialogContent>
    </Dialog>
  )
}