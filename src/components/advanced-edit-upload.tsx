import { ArrowUpFromLine } from "lucide-react";
import Upload from "./upload";
import { cn } from "@/lib/utils";
import { ImageInfoType } from "@/config";
// import { Input } from "./ui/input";
// import { Button } from "./ui/button";
// import { useCallback, useState } from "react";
// import { toast } from "sonner";

type AdvancedEditUploadProps = {
  className?: string;
  onSuccess?: (info: ImageInfoType) => void;
}

export default function AdvancedEditUpload({ className, onSuccess }: AdvancedEditUploadProps) {
  // const [url, setUrl] = useState('');
  // const handleImport = useCallback(async () => {
  //   if (url) {
  //     // 通过图片在线地址获取图片信息
  //     const imageInfo = await getImageInfoByUrl(url);
  //     onSuccess?.(imageInfo);
  //   } else {
  //     toast.error('请输入图片地址');
  //   }
  // }, [url, onSuccess]);

  return (
    <div className={cn("flex flex-col gap-4", className)}>
      {/* <div className="border w-full z-10 p-4 md:p-8 shadow-lg rounded-lg bg-background flex flex-col justify-center items-center gap-4">
        <h1 className="text-2xl font-bold leading-tight tracking-tighter"><Link className="inline-block mr-2 vertical-middle -mt-1" />从图片地址导入</h1>
        <p className="max-w-2xl text-lg text-foreground">输入图片地址，开始编辑</p>
        <div className="w-full">
          <form onSubmit={handleImport} className="flex w-full items-center space-x-2">
            <Input type="url" placeholder="https://example.com/image.jpg" className="w-full flex-1" value={url} onChange={(e) => setUrl(e.target.value)} />
            <Button type="submit" disabled={!url} onClick={handleImport}>导入</Button>
          </form>
        </div>
      </div> */}
      <div className="border w-full z-10 p-4 md:p-8 shadow-lg rounded-lg bg-background flex flex-col justify-center items-center gap-4">
        <h1 className="text-2xl font-bold leading-tight tracking-tighter"><ArrowUpFromLine className="inline-block mr-2 vertical-middle -mt-1" />从本地选择导入</h1>
        <p className="max-w-2xl text-lg text-foreground">上传图片，开始编辑</p>
        <Upload onSuccess={onSuccess} className="max-w-1/2 mx-auto" text="选择一张图片" />
      </div>
    </div>
  )
}