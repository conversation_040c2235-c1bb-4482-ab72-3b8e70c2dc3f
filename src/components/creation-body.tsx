import { useMemo, useEffect, useCallback } from "react";
import { useCreationStore } from "@/stores/creation";
import CreationBox from "./creation-box";
import { Palette } from "lucide-react";
import CreationInput from "./creation-input";
import { useDebounceEffect, useScroll } from "ahooks";
import { CreationTypeToggle } from "./creation-type-toggle";

export default function CreationBody() {
  const scroll = useScroll(document.getElementById('creation-scroll-container'));
  const {
    jobList,
    serverPullJobTotal,
    jobTotal,
    getJobHistoryFn,
  } = useCreationStore();

  const hasJob = useMemo(() => jobList.length > 0, [jobList]);
  const hasOtherJob = useMemo(() => serverPullJobTotal < jobTotal, [serverPullJobTotal, jobTotal]);
  const handlePullJobList = useCallback(() => {
    if ((scroll?.top === 0 && hasOtherJob) || jobList.length === 0) {
      console.info('handlePullJobList');
      getJobHistoryFn(jobList[0]?.id);
    }

    // if (jobList.length === 0) {
    //   creationScrollToBottom();
    // }
  }, [getJobHistoryFn, jobList, scroll?.top, hasOtherJob]);
  // 恢复滚动位置
  useEffect(() => {
    // handleScroll();
    const scrollPosition = localStorage.getItem('creation-scroll-position');

    if (scrollPosition) {
      document.getElementById('creation-scroll-container')?.scrollTo(0, parseInt(scrollPosition || '0'));
    }
  }, []);
  // useUpdateLayoutEffect(() => {
  //   if (jobLength > 0) {
  //     scrollToBottom();
  //   }
  // }, [jobLength]);
  useDebounceEffect(
    () => {
      handlePullJobList()
    },
    [scroll],
    {
      wait: 200,
    },
  );

  return (
    <div className="flex-1 h-screen relative flex flex-col">
      <div className="h-full flex-1 overflow-y-auto" id="creation-scroll-container">
        {hasJob ? (
          <div className="p-4 max-w-[1536px] w-full mx-auto">
            {/* <div className="text-sm bg-red-500 rounded px-2 py-1 text-white mb-3 inline-block">
              AI 创作
            </div> */}
            {jobList.map((item, index) => (
              <CreationBox key={item.job_id + index} job={item} />
            ))}
            <p className="text-xs text-muted-foreground text-center pt-3 opacity-50">已经是最新的了...</p>
            <div className="h-1 w-full opacity-0 mt-4" id="creation-scroll-bottom" />
          </div>
        ) : (
          <div className="h-full flex flex-col justify-center items-center gap-3">
            <h2 className="text-xl font-bold"><Palette className="inline-block mr-2 vertical-middle size-6 -mt-1" />开始您的创意之旅</h2>
            <p className="text-sm text-muted-foreground">在下方输入框中输入文字，按回车键开始生成图片</p>
          </div>
        )}
      </div>
      <div className="max-w-[1536px] w-full mx-auto relative px-4 py-2 xl:py-4 z-10 flex flex-col items-start">
        <CreationTypeToggle />
        <CreationInput />
      </div>
    </div>
  )
}