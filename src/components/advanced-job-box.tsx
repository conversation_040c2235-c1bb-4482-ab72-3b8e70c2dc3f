import { cn } from "@/lib/utils";
import { DiffusionStatus } from "@/stores/creation";
import { useUnmount } from "ahooks";
import { useInterval } from "ahooks";
import { useCallback, useMemo } from "react";
import customParseFormat from 'dayjs/plugin/customParseFormat' // ES 2015
import dayjs from "dayjs";
import 'dayjs/locale/zh-cn'
import { toast } from "sonner";
import { JobItemType, useAdvancedEditStore } from "@/stores/advanced-edit";
import { Button } from "./ui/button";
import { Trash2 } from "lucide-react";

dayjs.locale('zh-cn');
dayjs.extend(customParseFormat);

type CreationBoxProps = {
  job: JobItemType;
}
export default function CreationBox({
  job,
}: CreationBoxProps) {
  const {
    id,
    prompt,
    loading = false,
    data,
    status,
    cover,
    worktop,
  } = job;
  const { getJobInfo, updateJob, viewJobImage, removeJob } = useAdvancedEditStore();
  const [w, h] = useMemo(() => {
    const { width = 1, height = 1 } = worktop || {};

    return [width, height];
  }, [worktop])
  const handleGetJobInfo = useCallback(async () => {
    const res = await getJobInfo(id);
    const { status_code, data } = res;

    if (status_code === 1 && data.status === DiffusionStatus.COMPLETED) {
      updateJob(id, {
        id,
        status: data.status,
        data: data,
        loading: false,
        updatedAt: new Date().toISOString(),
      });
      toast.success(`生成完成`);
    } else if (status_code === 1 && data.status === DiffusionStatus.FAILED) {
      toast.error(data.comment || `生成失败`);
      updateJob(id, {
        id,
        status: data.status,
        data: data,
        loading: false,
        updatedAt: new Date().toISOString(),
      });
    }
  }, [getJobInfo, id, updateJob]);
  const handleGetScrollPosition = useCallback(() => {
    const scrollTop = document.getElementById('creation-scroll-container')?.scrollTop;
    localStorage.setItem('creation-scroll-position', scrollTop?.toString() || '0');
  }, []);
  const clearTimer = useInterval(() => {
    handleGetJobInfo()
  }, data ? undefined : 2000)

  useUnmount(() => {
    clearTimer()
  })

  return (
    <div className={cn("flex flex-col xl:flex-row gap-2 pb-4 xl:pb-2")}>
      <div className="w-14 relative">
        <div
          className="w-14 text-xs relative overflow-hidden bg-white-box dark:bg-black-box bg-8px rounded shadow-md cursor-pointer hover:shadow-lg hover:scale-150 hover:z-10 transition duration-300"
          style={{
            aspectRatio: `${w / h}`,
          }}
          onClick={() => {
            viewJobImage(id)
            handleGetScrollPosition()
          }}
        >
          <img src={cover} alt="图片已过期" className="contain-layout" />
        </div>
      </div>
      {status === DiffusionStatus.FAILED ? (
        <div className="flex-1 flex gap-4 justify-center items-center">
          <p className="text-xs text-muted-foreground text-center">生成失败</p>
          <Button variant="outline" size="icon" onClick={() => {
            removeJob(id)
          }}>
            <Trash2 />
          </Button>
        </div>
      ) : (
        <div className={cn("flex-1 gap-0.5 grid", {
          'grid-cols-2': w > h,
          'grid-cols-4': w <= h,
          "animate-pulse": loading
        })}>
          {loading ? [...Array(4)].map((_, index) => (
            <div
              key={index}
              className={cn("w-full h-full bg-zinc-200 dark:bg-zinc-800 animate-pulse flex justify-center items-center", {
                "rounded-l": index === 0 || (w > h && index % 2 === 0),
                "rounded-r": (w > h && index % 2 === 1) || index === 3,
              })}
              style={{
                aspectRatio: `${w / h}`,
              }}
            >
              <p className="text-muted-foreground text-xs">生成中</p>
            </div>
          )) : <>
            {data?.urls.map((url, index) => (
              <div
                key={url}
                className={cn("relative cursor-pointer overflow-hidden bg-zinc-200 dark:bg-zinc-800 shadow hover:shadow-lg hover:scale-150 hover:z-10 transition duration-300", {
                  "rounded-l": index === 0 || (w > h && index % 2 === 0),
                  "rounded-r": index === data?.urls.length - 1 || (w > h && index % 2 === 1),
                })}
                style={{
                  aspectRatio: `${w / h}`,
                }}
                onClick={() => {
                  viewJobImage(id, url)
                  handleGetScrollPosition()
                }}
              >
                <img
                  src={url}
                  key={url}
                  alt={prompt}
                  loading="lazy"
                  className="contain-layout"
                />
              </div>
            ))}
          </>}
        </div>
      )}
    </div>
  )
}