import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import CreationSearch from "./creation-search";
import { Button } from "./ui/button";
import { X } from "lucide-react";

type CreationInputPullProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
export default function CreationInputPull({ open, onOpenChange }: CreationInputPullProps) {
  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger className="absolute top-0 left-0" />
      <PopoverContent
        onPointerDownOutside={(e) => e.preventDefault()}
        // onInteractOutside={(e) => e.preventDefault()}
        // onEscapeKeyDown={(e) => e.preventDefault()}
        className="max-w-[1536px] w-screen h-[calc(100vh-400px)] overflow-y-auto p-0 relative bg-background"
        align="start"
        sideOffset={4}
      >
        <Button variant="outline" size="icon" className="absolute top-2 right-2 z-20" onClick={() => onOpenChange(false)}>
          <X className="size-4" />
        </Button>
        <CreationSearch />
      </PopoverContent>
    </Popover>
  )
}