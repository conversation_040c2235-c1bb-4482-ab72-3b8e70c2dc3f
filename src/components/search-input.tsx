import { useCreationStore } from "@/stores/creation";
import { AutoTextarea } from "./ui/auto-textarea";
import { useCallback, useMemo } from "react";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import { Loader2, Search } from "lucide-react";

type SearchInputProps = {
  className?: string;
  onSearch?: () => void;
}
export default function SearchInput({
  className,
  onSearch,
}: SearchInputProps) {
  const {
    searchQuery,
    searchLoading,
    setSearchQuery,
    searchImageFn,
  } = useCreationStore();
  const hasPrompt = useMemo(() => searchQuery.length > 0, [searchQuery]);
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSearchQuery(e.target.value);
  }
  const handleSearch = useCallback(() => {
    searchImageFn();
    onSearch?.();
  }, [searchImageFn, onSearch]);
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 如果是组合键，则不处理
    if (e.metaKey || e.shiftKey || e.ctrlKey) {
      return;
    }
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  }, [handleSearch]);

  return (
    <div className={cn("relative", className)}>
      <div className="flex flex-col w-full rounded-xl shadow-lg border bg-zinc-50 dark:bg-zinc-900">
        <div className="flex gap-2 p-2">
          <AutoTextarea
            className="focus-visible:ring-0 border-none shadow-none resize-none min-h-[32px] py-1.5 px-2"
            placeholder="输入图片描述，按回车键开始搜索"
            value={searchQuery}
            disabled={searchLoading}
            onChange={handleTextChange}
            onKeyDown={handleKeyDown}
          />
          <Button variant="ghost" size="icon" title="搜索" disabled={!hasPrompt || searchLoading} className="size-8 text-muted-foreground" onClick={handleSearch}>
            {searchLoading ? <Loader2 className="size-6 animate-spin" /> : <Search className="size-6" />}
          </Button>
        </div>
      </div>
    </div>
  )
}