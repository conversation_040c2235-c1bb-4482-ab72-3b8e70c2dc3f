import { AutoTextarea } from "./ui/auto-textarea";
import { useCallback, useMemo } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "./ui/button";
import { Image, Blend, SlidersHorizontal, Smile, X, Trash2, Send, LoaderCircle, ImagePlus, Sparkles } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { CreationSetting } from "./creation-setting";
import { ToggleGroup, ToggleGroupItem } from "./ui/toggle-group";
import { EstablishingShotClassType, useAdvancedEditStore } from "@/stores/advanced-edit";
import { EstablishingShotBox } from "./establishing-shot-box";

type AdvancedEditInputProps = {
  className?: string;
  onGenerate?: () => void;
}
export default function AdvancedEditInput({
  className,
  onGenerate,
}: AdvancedEditInputProps) {
  const {
    prompt,
    setPrompt,
    foregrounds,
    generate,
    retexture,
    establishingShot,
    removeEstablishingShot,
    updateEstablishingShotType,
    clearEstablishingShot,
    addEstablishingShot,
    activeType,
    isGenerating,
    suggestPrompt,
  } = useAdvancedEditStore();
  // const hasPrompt = useMemo(() => prompt.length > 0, [prompt]);
  const hasForeground = useMemo(() => foregrounds.length > 0, [foregrounds]);
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
  }
  const handleGenerate = useCallback(() => {
    if (activeType === 'edit') {
      generate();
    } else {
      retexture();
    }
    onGenerate?.();
  }, [generate, retexture, onGenerate, activeType]);
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 如果是组合键，则不处理
    if (e.metaKey || e.shiftKey || e.ctrlKey) {
      return;
    }
    if (e.key === 'Enter') {
      e.preventDefault();
      handleGenerate();
    }
  }, [handleGenerate]);
  const establishingShotClassList = useMemo(() => {
    const centent = establishingShot.filter(({ type }) => type === 'content');
    const sref = establishingShot.filter(({ type }) => type === 'sref');
    const cref = establishingShot.filter(({ type }) => type === 'cref');
    const result = [];

    if (centent.length > 0) {
      result.push(centent);
    }
    if (sref.length > 0) {
      result.push(sref);
    }
    if (cref.length > 0) {
      result.push(cref);
    }

    return result;
  }, [establishingShot]);

  return (
    <div className={cn("relative", className)}>
      <div className="flex flex-col w-full rounded-xl shadow-lg border bg-zinc-50 dark:bg-zinc-900 p-2 gap-2">
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" title="添加垫图" className="size-8 text-muted-foreground">
                <ImagePlus className="size-6" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              sideOffset={14}
              align="start"
              alignOffset={-10}
              className="rounded-xl shadow-md container w-full p-3 bg-zinc-50 dark:bg-zinc-900"
            >
              <EstablishingShotBox onClick={(establishingShot) => {
                addEstablishingShot([{
                  ...establishingShot,
                  type: 'content',
                }]);
              }} />
            </PopoverContent>
          </Popover>
          <AutoTextarea
            className="focus-visible:ring-0 border-none shadow-none resize-none min-h-[32px] max-h-[72px] overflow-y-auto py-1.5 px-2"
            placeholder="输入文字，按回车键开始生成图片"
            value={prompt}
            onChange={handleTextChange}
            onKeyDown={handleKeyDown}
          />
          <Button
            variant="ghost"
            size="icon"
            title="建议提示词"
            className="size-8 text-muted-foreground hidden"
            onClick={suggestPrompt}
          >
            <Sparkles className="size-6" />
          </Button>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" title="高级设置" className="size-8 text-muted-foreground">
                <SlidersHorizontal className="size-6" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              sideOffset={14}
              align="end"
              alignOffset={-10}
              className="rounded-xl shadow-md container w-full p-3 bg-zinc-50 dark:bg-zinc-900"
            >
              <CreationSetting />
            </PopoverContent>
          </Popover>
          <Button
            variant="outline"
            title="生成"
            className="h-8 text-muted-foreground text-sm font-normal"
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.length || !hasForeground}
          >
            {isGenerating ? <LoaderCircle className="size-5 animate-spin" /> : <Send className="size-5" />}生成{activeType === 'edit' ? '编辑' : '转绘'}
          </Button>
        </div>
        {establishingShotClassList.length > 0 && (
          <div className="flex gap-2">
            <div className="flex flex-wrap divide-x -ml-2">
              {establishingShotClassList.map((establishingShotClass, index) => (
                <div className="flex flex-wrap gap-1 px-2" key={`establishing-shot-class-${index}`}>
                  {establishingShotClass.map(({ id, url, type, name }, itemIndex) => (
                    <div className="relative size-14 overflow-hidden rounded-md group border" key={`${id}-${index}-class-${type}-${itemIndex}`}>
                      <img src={url} className="w-full h-full object-cover" alt={type} />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute z-20 top-0 right-0 size-6 p-1 text-muted-foreground rounded-none rounded-bl-lg"
                        onClick={() => removeEstablishingShot(id, type)}
                      >
                        <X className="size-4" />
                      </Button>
                      <div className="absolute bottom-1 right-1 z-10 size-4 bg-background/80 backdrop-blur-sm shadow-sm rounded-sm p-0.5">
                        {type === 'content' && (
                          <Image className="size-3" />
                        )}
                        {type === 'sref' && (
                          <Blend className="size-3" />
                        )}
                        {type === 'cref' && (
                          <Smile className="size-3" />
                        )}
                      </div>
                      <div
                        className="absolute bottom-0 left-0 z-10 w-full py-1 flex items-center justify-center bg-muted/50 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        title={name}
                      >
                        <ToggleGroup
                          type="single"
                          size="sm"
                          value={type}
                          className="gap-0.5"
                          onValueChange={(value) => updateEstablishingShotType(id, type, value as EstablishingShotClassType)}
                        >
                          <ToggleGroupItem
                            value="content"
                            title="内容"
                            aria-label="Toggle content"
                            className="size-4 p-0.5 min-w-4 rounded-sm"
                          >
                            <Image className="!size-3" />
                          </ToggleGroupItem>
                          <ToggleGroupItem
                            value="sref"
                            title="风格"
                            aria-label="Toggle sref"
                            className="size-4 p-0.5 min-w-4 rounded-sm"
                          >
                            <Blend className="!size-3" />
                          </ToggleGroupItem>
                          <ToggleGroupItem
                            value="cref"
                            title="人脸"
                            aria-label="Toggle cref"
                            className="size-4 p-0.5 min-w-4 rounded-sm"
                          >
                            <Smile className="!size-3" />
                          </ToggleGroupItem>
                        </ToggleGroup>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
            <Button variant="ghost" size="icon" title="清空垫图" className="size-8 text-muted-foreground ml-auto" onClick={() => clearEstablishingShot()}>
              <Trash2 className="size-5" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}