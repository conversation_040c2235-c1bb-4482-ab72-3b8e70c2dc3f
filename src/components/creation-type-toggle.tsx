import { Image, Newspaper, Film } from "lucide-react";
import { ToggleGroup, ToggleGroupItem } from "./ui/toggle-group";
import { GenerationType, useCreationStore } from "@/stores/creation";

export function CreationTypeToggle() {
  const { generationType, setGenerationType } = useCreationStore();

  return (
    <ToggleGroup
      type="single"
      variant="outline"
      value={generationType}
      onValueChange={(value) => {
        if (value) setGenerationType(value as GenerationType)
      }}
      className="gap-1 z-10 -mb-[1px]"
    >
      <ToggleGroupItem value="image" className="font-normal gap-1 rounded-b-none shadow-none bg-muted/80 backdrop-blur-sm data-[state=on]:bg-zinc-50 dark:data-[state=on]:bg-zinc-900 data-[state=on]:border-b-transparent" aria-label="切换到图片创作">
        <Image className="size-4" />图片
      </ToggleGroupItem>
      <ToggleGroupItem value="video" className="font-normal gap-1 rounded-b-none shadow-none bg-muted/80 backdrop-blur-sm data-[state=on]:bg-zinc-50 dark:data-[state=on]:bg-zinc-900 data-[state=on]:border-b-transparent" aria-label="切换到视频创作">
        <Film className="size-4" />视频
      </ToggleGroupItem>
      <ToggleGroupItem value="article" className="font-normal gap-1 rounded-b-none shadow-none bg-muted/80 backdrop-blur-sm data-[state=on]:bg-zinc-50 dark:data-[state=on]:bg-zinc-900 data-[state=on]:border-b-transparent" aria-label="切换到文章创作">
        <Newspaper className="size-4" />文章
      </ToggleGroupItem>
    </ToggleGroup>
  )
}