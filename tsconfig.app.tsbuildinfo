{"root": ["./src/error-page.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/advanced-edit-input.tsx", "./src/components/advanced-edit-upload.tsx", "./src/components/advanced-edit.tsx", "./src/components/advanced-job-box.tsx", "./src/components/advanced-layer-list.tsx", "./src/components/advanced-move-layer.tsx", "./src/components/advanced-upload-dialog.tsx", "./src/components/basemap.tsx", "./src/components/copyright-tag.tsx", "./src/components/creation-body.tsx", "./src/components/creation-box.tsx", "./src/components/creation-edit-body.tsx", "./src/components/creation-edit.tsx", "./src/components/creation-input-pull.tsx", "./src/components/creation-input.tsx", "./src/components/creation-prompt.tsx", "./src/components/creation-search.tsx", "./src/components/creation-setting.tsx", "./src/components/creation-type-toggle.tsx", "./src/components/cut-box.tsx", "./src/components/edit.tsx", "./src/components/establishing-shot-box.tsx", "./src/components/foreground-box.tsx", "./src/components/foreground.tsx", "./src/components/home-body.tsx", "./src/components/login-body.tsx", "./src/components/mode-toggle.tsx", "./src/components/move-layer.tsx", "./src/components/search-box.tsx", "./src/components/search-input.tsx", "./src/components/theme-provider.tsx", "./src/components/upload-real.tsx", "./src/components/upload.tsx", "./src/components/usetheme.ts", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/auto-textarea.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/config/index.ts", "./src/hooks/use-advanced-upload-success.ts", "./src/hooks/use-mobile.tsx", "./src/hooks/use-toast.ts", "./src/lib/utils.ts", "./src/loader/creation_detail.ts", "./src/routes/advanced_edit.tsx", "./src/routes/advanced_edit_new.tsx", "./src/routes/creation_detail.tsx", "./src/routes/home.tsx", "./src/routes/login.tsx", "./src/routes/root.tsx", "./src/stores/advanced-edit.ts", "./src/stores/common.ts", "./src/stores/creation.ts", "./src/stores/cutbox.ts", "./src/stores/edit.ts", "./src/stores/establishingshot.ts", "./src/stores/foreground.ts", "./src/stores/tier.ts", "./src/workers/canva.ts"], "version": "5.8.3"}